using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using API.Controllers.ManagedAccounts;
using API.Helpers;
using ManagedAccounts.Models.Commands;
using ManagedAccounts.Models.DTOs;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace API.UnitTest
{
    public class ManagedAccountControllerTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly Mock<ILogger<ManagedAccountController>> _mockLogger;
        private readonly ManagedAccountController _controller;

        public ManagedAccountControllerTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockHelperService = new Mock<IHelperService>();
            _mockLogger = new Mock<ILogger<ManagedAccountController>>();
            _controller = new ManagedAccountController(_mockMediator.Object, _mockHelperService.Object, _mockLogger.Object);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_NullMediator_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ManagedAccountController(null, _mockHelperService.Object, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_NullHelperService_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ManagedAccountController(_mockMediator.Object, null, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_NullLogger_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ManagedAccountController(_mockMediator.Object, _mockHelperService.Object, null));
        }

        #endregion

        #region CreateManagedAccount Tests

        [Fact]
        public async Task CreateManagedAccount_ValidCommand_ReturnsCreatedResult()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Managed Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "ABC Investment Management",
                Administrator = "XYZ Administration Services",
                Custodian = "DEF Custody Bank",
                LegalCounsel = "GHI Legal Services",
                LEI = "12345678901234567890",
                InvestmentSummary = "Investment in technology sector companies"
            };

            var expectedId = Guid.NewGuid();
            var expectedResult = CreateManagedAccountResult.Success(expectedId);
            var expectedUserId = 123;

            _mockHelperService.Setup(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(expectedUserId);
            _mockMediator.Setup(x => x.Send(It.IsAny<CreateManagedAccountCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.CreateManagedAccount(command);

            // Assert
            var createdResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(nameof(_controller.GetManagedAccount), createdResult.ActionName);

            // Check the anonymous object properties
            var value = createdResult.Value;
            var idProperty = value?.GetType().GetProperty("Id");
            var messageProperty = value?.GetType().GetProperty("Message");

            Assert.NotNull(idProperty);
            Assert.NotNull(messageProperty);
            Assert.Equal(expectedId, idProperty.GetValue(value));
            Assert.Equal("Managed account created successfully", messageProperty.GetValue(value));

            // Verify that CreatedBy was set
            Assert.Equal(expectedUserId, command.CreatedBy);

            _mockHelperService.Verify(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
            _mockMediator.Verify(x => x.Send(command, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateManagedAccount_FailureResult_ReturnsBadRequest()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Account",
                Domicile = "United States"
            };

            var errorMessage = "Account name already exists";
            var failureResult = CreateManagedAccountResult.Failure(errorMessage);
            var expectedUserId = 123;

            _mockHelperService.Setup(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(expectedUserId);
            _mockMediator.Setup(x => x.Send(It.IsAny<CreateManagedAccountCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(failureResult);

            // Act
            var result = await _controller.CreateManagedAccount(command);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);

            // Check the anonymous object properties
            var value = badRequestResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal(errorMessage, errorProperty.GetValue(value));

            _mockHelperService.Verify(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
            _mockMediator.Verify(x => x.Send(command, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateManagedAccount_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Account",
                Domicile = "United States"
            };

            var expectedUserId = 123;

            _mockHelperService.Setup(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(expectedUserId);
            _mockMediator.Setup(x => x.Send(It.IsAny<CreateManagedAccountCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.CreateManagedAccount(command);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);

            // Check the anonymous object properties
            var value = statusCodeResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal("An unexpected error occurred while creating the managed account", errorProperty.GetValue(value));

            _mockHelperService.Verify(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
            _mockMediator.Verify(x => x.Send(command, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateManagedAccount_HelperServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Account",
                Domicile = "United States"
            };

            _mockHelperService.Setup(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Throws(new Exception("User context error"));

            // Act
            var result = await _controller.CreateManagedAccount(command);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);

            // Check the anonymous object properties
            var value = statusCodeResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal("An unexpected error occurred while creating the managed account", errorProperty.GetValue(value));

            _mockHelperService.Verify(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
            _mockMediator.Verify(x => x.Send(It.IsAny<CreateManagedAccountCommand>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        #endregion

        #region GetManagedAccount Tests

        [Fact]
        public async Task GetManagedAccount_ValidId_ReturnsOkResult()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var expectedAccount = new ManagedAccountResponseDto
            {
                Id = accountId,
                Name = "Test Managed Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "ABC Investment Management",
                Administrator = "XYZ Administration Services",
                Custodian = "DEF Custody Bank",
                LegalCounsel = "GHI Legal Services",
                LEI = "12345678901234567890",
                InvestmentSummary = "Investment in technology sector companies",
                CreatedOn = DateTime.UtcNow
            };

            var successResult = GetManagedAccountResult.Success(expectedAccount);

            _mockMediator.Setup(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            var result = await _controller.GetManagedAccount(accountId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedAccount = Assert.IsType<ManagedAccountResponseDto>(okResult.Value);
            Assert.Equal(accountId, returnedAccount.Id);
            Assert.Equal("Test Managed Account", returnedAccount.Name);

            _mockMediator.Verify(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetManagedAccount_AccountNotFound_ReturnsNotFound()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var errorMessage = $"Managed account with ID {accountId} not found";
            var failureResult = GetManagedAccountResult.Failure(errorMessage);

            _mockMediator.Setup(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(failureResult);

            // Act
            var result = await _controller.GetManagedAccount(accountId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);

            // Check the anonymous object properties
            var value = notFoundResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal(errorMessage, errorProperty.GetValue(value));

            _mockMediator.Verify(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetManagedAccount_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var accountId = Guid.NewGuid();

            _mockMediator.Setup(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.GetManagedAccount(accountId);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);

            // Check the anonymous object properties
            var value = statusCodeResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal("An unexpected error occurred while retrieving the managed account", errorProperty.GetValue(value));

            _mockMediator.Verify(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetManagedAccount_EmptyGuid_ReturnsNotFound()
        {
            // Arrange
            var accountId = Guid.Empty;
            var errorMessage = "Managed account with ID ********-0000-0000-0000-************ not found";
            var failureResult = GetManagedAccountResult.Failure(errorMessage);

            _mockMediator.Setup(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(failureResult);

            // Act
            var result = await _controller.GetManagedAccount(accountId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);

            // Check the anonymous object properties
            var value = notFoundResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal(errorMessage, errorProperty.GetValue(value));

            _mockMediator.Verify(x => x.Send(It.Is<GetManagedAccountQuery>(q => q.Id == accountId), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetAllManagedAccounts Tests

        [Fact]
        public async Task GetAllManagedAccounts_ValidRequest_ReturnsOkResult()
        {
            // Arrange
            var expectedAccounts = new List<ManagedAccountSummaryDto>
            {
                new ManagedAccountSummaryDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Account 1",
                    Domicile = "United States",
                    CommencementDate = DateTime.UtcNow,
                    BaseCurrency = "USD",
                    InvestmentManager = "Manager 1",
                    CreatedOn = DateTime.UtcNow
                },
                new ManagedAccountSummaryDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Account 2",
                    Domicile = "United Kingdom",
                    CommencementDate = DateTime.UtcNow.AddDays(-30),
                    BaseCurrency = "GBP",
                    InvestmentManager = "Manager 2",
                    CreatedOn = DateTime.UtcNow.AddDays(-30)
                }
            };

            var successResult = GetAllManagedAccountsResult.Success(expectedAccounts);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            var result = await _controller.GetAllManagedAccounts();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedAccounts = Assert.IsAssignableFrom<IEnumerable<ManagedAccountSummaryDto>>(okResult.Value);
            Assert.Equal(2, returnedAccounts.Count());
            Assert.Equal("Account 1", returnedAccounts.First().Name);
            Assert.Equal("Account 2", returnedAccounts.Last().Name);

            _mockMediator.Verify(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllManagedAccounts_EmptyResult_ReturnsOkWithEmptyList()
        {
            // Arrange
            var emptyAccounts = new List<ManagedAccountSummaryDto>();
            var successResult = GetAllManagedAccountsResult.Success(emptyAccounts);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            var result = await _controller.GetAllManagedAccounts();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedAccounts = Assert.IsAssignableFrom<IEnumerable<ManagedAccountSummaryDto>>(okResult.Value);
            Assert.Empty(returnedAccounts);

            _mockMediator.Verify(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllManagedAccounts_FailureResult_ReturnsBadRequest()
        {
            // Arrange
            var errorMessage = "Failed to retrieve managed accounts";
            var failureResult = GetAllManagedAccountsResult.Failure(errorMessage);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(failureResult);

            // Act
            var result = await _controller.GetAllManagedAccounts();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);

            // Check the anonymous object properties
            var value = badRequestResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal(errorMessage, errorProperty.GetValue(value));

            _mockMediator.Verify(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllManagedAccounts_MediatorThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.GetAllManagedAccounts();

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);

            // Check the anonymous object properties
            var value = statusCodeResult.Value;
            var errorProperty = value?.GetType().GetProperty("Error");
            Assert.NotNull(errorProperty);
            Assert.Equal("An unexpected error occurred while retrieving managed accounts", errorProperty.GetValue(value));

            _mockMediator.Verify(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllManagedAccounts_SingleAccount_ReturnsOkWithSingleItem()
        {
            // Arrange
            var singleAccount = new List<ManagedAccountSummaryDto>
            {
                new ManagedAccountSummaryDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Single Account",
                    Domicile = "Canada",
                    CommencementDate = DateTime.UtcNow,
                    BaseCurrency = "CAD",
                    InvestmentManager = "Canadian Manager",
                    CreatedOn = DateTime.UtcNow
                }
            };

            var successResult = GetAllManagedAccountsResult.Success(singleAccount);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            var result = await _controller.GetAllManagedAccounts();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedAccounts = Assert.IsAssignableFrom<IEnumerable<ManagedAccountSummaryDto>>(okResult.Value);
            Assert.Single(returnedAccounts);
            Assert.Equal("Single Account", returnedAccounts.First().Name);
            Assert.Equal("Canada", returnedAccounts.First().Domicile);

            _mockMediator.Verify(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Integration Tests

        [Fact]
        public async Task CreateManagedAccount_SetsCreatedByFromCurrentUser()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Account",
                Domicile = "United States"
            };

            var expectedUserId = 456;
            var expectedId = Guid.NewGuid();
            var successResult = CreateManagedAccountResult.Success(expectedId);

            _mockHelperService.Setup(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(expectedUserId);
            _mockMediator.Setup(x => x.Send(It.IsAny<CreateManagedAccountCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            await _controller.CreateManagedAccount(command);

            // Assert
            Assert.Equal(expectedUserId, command.CreatedBy);
            _mockHelperService.Verify(x => x.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
        }

        [Fact]
        public async Task GetManagedAccount_CallsMediatorWithCorrectQuery()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var successResult = GetManagedAccountResult.Success(new ManagedAccountResponseDto
            {
                Id = accountId,
                Name = "Test Account",
                CreatedOn = DateTime.UtcNow
            });

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedAccountQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            await _controller.GetManagedAccount(accountId);

            // Assert
            _mockMediator.Verify(x => x.Send(
                It.Is<GetManagedAccountQuery>(q => q.Id == accountId),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllManagedAccounts_CallsMediatorWithCorrectQuery()
        {
            // Arrange
            var successResult = GetAllManagedAccountsResult.Success(new List<ManagedAccountSummaryDto>());

            _mockMediator.Setup(x => x.Send(It.IsAny<GetAllManagedAccountsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult);

            // Act
            await _controller.GetAllManagedAccounts();

            // Assert
            _mockMediator.Verify(x => x.Send(
                It.IsAny<GetAllManagedAccountsQuery>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion
    }
}

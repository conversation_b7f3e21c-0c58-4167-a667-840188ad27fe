using Contract.PortfolioCompany;
using DataAccessLayer.Models.DashboardTracker;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Helpers;
using Contract.Currency;
using DataAccessLayer.DBModel;

namespace DocumentCollection.UnitTest.Helpers
{
    public class DashboardTrackerHelperTest
    {
        [Fact]
        public void GenerateColumnsDtoArray_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>();

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GenerateColumnsDtoArray_ReturnsNonTimeSeriesColumn_WhenFieldTypeIsNot2()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 1,
                    DataType = 1,
                    Name = "Test Column",
                    IsActive = true,
                    IsDeleted = false
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(1, result[0].ID);
            Assert.Equal("Test Column", result[0].Name);
            Assert.False(result[0].IsTimeSeries);
            Assert.Null(result[0].TimeSeriesID);
        }

        [Fact]
        public void GenerateTimeSeriesColumns_ReturnsEmpty_WhenRequiredFieldsAreMissing()
        {
            // Arrange
            var config = new DashboardTrackerConfig
            {
                ID = 1,
                FieldType = 2,
                StartPeriod = null, // Missing required field
                EndPeriod = "2024",
                FrequencyType = 1
            };

            // Act
            var result = DashboardTrackerHelper.GenerateTimeSeriesColumns(config);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GeneratePeriodTexts_Monthly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "01/2023";
            string endPeriod = "01/2023";
            int frequencyType = 1; // Monthly
            string format = "MMM yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Contains("Jan 2023", result[0].DisplayText);
            Assert.Contains("01_2023", result[0].Value);
        }

        [Fact]
        public void GeneratePeriodTexts_Quarterly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "Q1 2023";
            string endPeriod = "Q2 2023";
            int frequencyType = 2; // Quarterly
            string format = "Q yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            var displayResult = result.Select(r => r.DisplayText).ToList();
            var fieldidResult = result.Select(r => r.Value).ToList();
            Assert.Contains("Q1 2023", displayResult);
            Assert.Contains("Q2 2023", displayResult);
            Assert.Contains("Q1_2023", fieldidResult);
            Assert.Contains("Q2_2023", fieldidResult);
        }

        [Fact]
        public void GeneratePeriodTexts_Yearly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "2023";
            string endPeriod = "2025";
            int frequencyType = 3; // Yearly
            string format = "yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);
            var displayResult = result.Select(r => r.DisplayText).ToList();
            var fieldidResult = result.Select(r => r.Value).ToList();
            Assert.Contains("2023", displayResult);
            Assert.Contains("2024", displayResult);
            Assert.Contains("2025", displayResult);
            Assert.Contains("2023", fieldidResult);
            Assert.Contains("2024", fieldidResult);
            Assert.Contains("2025", fieldidResult);
        }

        [Fact]
        public void GenerateMonthlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "01/2023";
            string endPeriod = "01/2023";
            string format = "MMM yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateMonthlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public void GenerateQuarterlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "Q1 2023";
            string endPeriod = "Q4 2023";
            string format = "QX yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateQuarterlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(4, result.Count);
            var displayResult = result.Select(r => r.DisplayText).ToList();
            var fieldidResult = result.Select(r => r.Value).ToList();
            Assert.Contains("Q1 2023", displayResult);
            Assert.Contains("Q2 2023", displayResult);
            Assert.Contains("Q3 2023", displayResult);
            Assert.Contains("Q4 2023", displayResult);
            Assert.Contains("Q1_2023", fieldidResult);
            Assert.Contains("Q2_2023", fieldidResult);
            Assert.Contains("Q3_2023", fieldidResult);
            Assert.Contains("Q4_2023", fieldidResult);
        }

        [Fact]
        public void GenerateYearlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "2023";
            string endPeriod = "2025";
            string format = "yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateYearlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);
            var displayResult = result.Select(r => r.DisplayText).ToList();
            var fieldidResult = result.Select(r => r.Value).ToList();
            Assert.Contains("2023", displayResult);
            Assert.Contains("2024", displayResult);
            Assert.Contains("2025", displayResult);
        }

        [Fact]
        public void TryParseQuarter_ReturnsTrue_ForValidQ1Format()
        {
            // Arrange
            string quarterString = "Q1 2023";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.True(result);
            Assert.Equal(2023, year);
            Assert.Equal(1, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsTrue_ForValidYearFirstFormat()
        {
            // Arrange
            string quarterString = "2023 Q2";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.True(result);
            Assert.Equal(2023, year);
            Assert.Equal(2, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForInvalidFormat()
        {
            // Arrange
            string quarterString = "Invalid Quarter";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
            Assert.Equal(0, year);
            Assert.Equal(0, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForEmptyString()
        {
            // Arrange
            string quarterString = "";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
            Assert.Equal(0, year);
            Assert.Equal(0, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForInvalidQuarterNumber()
        {
            // Arrange
            string quarterString = "Q5 2023"; // Invalid quarter number

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_ReturnsRows_WithStaticAndDynamicColumns()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://site.com",
                    ReportingCurrencyDetail = new CurrencyModel { Currency = "USD" },
                    MasterCompanyName = "Master1",
                    FinancialYearEnd = "2024",
                    CompanyLegalName = "Legal1"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "DropdownCol", DataType = 4, FieldType = 1 },
                new ColumnsDto { ID = 2, Name = "Website", DataType = 1, FieldType = 1, MapTo = MapWith.Website },
                new ColumnsDto { ID = 3, Name = "Currency", DataType = 1, FieldType = 1, MapTo = MapWith.Currency },
                new ColumnsDto { ID = 4, Name = "MasterCompanyName", DataType = 1, FieldType = 1, MapTo = MapWith.MasterCompanyName },
                new ColumnsDto { ID = 5, Name = "FinancialYearEnd", DataType = 1, FieldType = 1, MapTo = MapWith.FinancialYearEnd },
                new ColumnsDto { ID = 6, Name = "CompanyLegalName", DataType = 1, FieldType = 1, MapTo = MapWith.CompanyLegalName },
                new ColumnsDto { ID = 7, Name = "EmptyCol", DataType = 1, FieldType = 1 }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("Fund1", row["Fund"]);
            Assert.Equal("Company1", row["Portfolio Company"]);
            Assert.Equal("logo1.png", row["CompanyLogo"]);
            Assert.Equal("http://site.com", row["Website"]);
            Assert.Equal("USD", row["Currency"]);
            Assert.Equal("Master1", row["MasterCompanyName"]);
            Assert.Equal("2024", row["FinancialYearEnd"]);
            Assert.Equal("Legal1", row["CompanyLegalName"]);
            Assert.Equal(string.Empty, row["EmptyCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_HandlesMissingImage_AndEmptyDropdown()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund2",
                    CompanyName = "Company2",
                    PortfolioCompanyID = 2,
                    Website = null,
                    ReportingCurrencyDetail = new CurrencyModel { Currency = null },
                    MasterCompanyName = null,
                    FinancialYearEnd = null,
                    CompanyLegalName = null
                }
            };
            var imagedata = new Dictionary<int, string>();
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "DropdownCol", DataType = 4, FieldType = 1 },
                new ColumnsDto { ID = 2, Name = "Website", DataType = 1, FieldType = 1, MapTo = MapWith.Website },
                new ColumnsDto { ID = 3, Name = "Currency", DataType = 1, FieldType = 1, MapTo = MapWith.Currency },
                new ColumnsDto { ID = 4, Name = "MasterCompanyName", DataType = 1, FieldType = 1, MapTo = MapWith.MasterCompanyName },
                new ColumnsDto { ID = 5, Name = "FinancialYearEnd", DataType = 1, FieldType = 1, MapTo = MapWith.FinancialYearEnd },
                new ColumnsDto { ID = 6, Name = "CompanyLegalName", DataType = 1, FieldType = 1, MapTo = MapWith.CompanyLegalName },
                new ColumnsDto { ID = 7, Name = "EmptyCol", DataType = 1, FieldType = 1 }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal(string.Empty, row["CompanyLogo"]);
            Assert.Null(row["Website"]);
            Assert.Null(row["Currency"]);
            Assert.Null(row["MasterCompanyName"]);
            Assert.Null(row["FinancialYearEnd"]);
            Assert.Null(row["CompanyLegalName"]);
            Assert.Equal(string.Empty, row["EmptyCol"]);
            Assert.Equal(string.Empty, row["DropdownCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCellValues_ReturnsStoredValues()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://site.com",
                    ReportingCurrencyDetail = new CurrencyModel { Currency = "USD" },
                    MasterCompanyName = "Master1",
                    FinancialYearEnd = "2024",
                    CompanyLegalName = "Legal1"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "CustomColumn", DataType = 1, FieldType = 1, TimeSeriesID = string.Empty },
                new ColumnsDto { ID = 2, Name = "TimeSeriesCol", DataType = 2, FieldType = 2, TimeSeriesID = "2_1", IsTimeSeries = true }
            };
            var cellValues = new List<DashboardTrackerCellValue>
            {
                new DashboardTrackerCellValue
                {
                    PortfolioCompanyId = 1,
                    ColumnId = 1,
                    TimeSeriesID = string.Empty,
                    CellValue = "Stored Value 1"
                },
                new DashboardTrackerCellValue
                {
                    PortfolioCompanyId = 1,
                    ColumnId = 2,
                    TimeSeriesID = "2_1",
                    CellValue = "Stored TimeSeries Value"
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, cellValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("Fund1", row["Fund"]);
            Assert.Equal("Company1", row["Portfolio Company"]);
            Assert.Equal("logo1.png", row["CompanyLogo"]);
            Assert.Equal("Stored Value 1", row["CustomColumn"]);
            Assert.Equal("Stored TimeSeries Value", row["TimeSeriesCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithPartialCellValues_FallsBackToDefault()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://site.com",
                    ReportingCurrencyDetail = new CurrencyModel { Currency = "USD" },
                    MasterCompanyName = "Master1",
                    FinancialYearEnd = "2024",
                    CompanyLegalName = "Legal1"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "CustomColumn", DataType = 1, FieldType = 1, TimeSeriesID = string.Empty },
                new ColumnsDto { ID = 2, Name = "Website", DataType = 1, FieldType = 1, MapTo = MapWith.Website, TimeSeriesID = string.Empty }
            };
            var dropdownValuesDict = new Dictionary<int, List<string>>();
            var cellValues = new List<DashboardTrackerCellValue>
            {
                new DashboardTrackerCellValue
                {
                    PortfolioCompanyId = 1,
                    ColumnId = 1,
                    TimeSeriesID = string.Empty,
                    CellValue = "Stored Value 1"
                }
                // No stored value for column 2, should fall back to MapTo logic
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, cellValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("Stored Value 1", row["CustomColumn"]);
            Assert.Equal("http://site.com", row["Website"]); // Falls back to MapTo logic
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_ReturnsCustomFieldValue()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
            new PortfolioCompanyQueryModel
            {
                FundId = 1,
                FundName = "FundA",
                CompanyName = "CompanyA",
                PortfolioCompanyID = 101
            }
            };
            var imagedata = new Dictionary<int, string> { { 101, "logoA.png" } };
            var columnsDto = new List<ColumnsDto>
            {
            new ColumnsDto
            {
                ID = 10,
                Name = "CustomFieldCol",
                DataType = 1,
                FieldType = 1,
                MapTo = (MapWith)999, // Arbitrary int for custom field
                MapToType = MapToType.CustomFields
            }
            };
            var customFieldValues = new List<PageConfigurationFieldValue>
            {
            new PageConfigurationFieldValue
            {
                PageFeatureId = 101, // matches PortfolioCompanyID
                FieldID = 999, // matches MapTo
                FieldValue = "CustomValue999"
            }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("CustomValue999", row["CustomFieldCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_NoMatch_ReturnsEmpty()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
            new PortfolioCompanyQueryModel
            {
                FundId = 2,
                FundName = "FundB",
                CompanyName = "CompanyB",
                PortfolioCompanyID = 102
            }
            };
            var imagedata = new Dictionary<int, string> { { 102, "logoB.png" } };
            var columnsDto = new List<ColumnsDto>
            {
            new ColumnsDto
            {
                ID = 11,
                Name = "CustomFieldCol",
                DataType = 1,
                FieldType = 1,
                MapTo = (MapWith)888, // Arbitrary int for custom field
                MapToType = MapToType.CustomFields
            }
            };
            var customFieldValues = new List<PageConfigurationFieldValue>
            {
            new PageConfigurationFieldValue
            {
                PageFeatureId = 999, // does not match PortfolioCompanyID
                FieldID = 888,
                FieldValue = "ShouldNotAppear"
            }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal(string.Empty, row["CustomFieldCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithNullCustomFieldValues_ReturnsEmpty()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
            new PortfolioCompanyQueryModel
            {
                FundId = 3,
                FundName = "FundC",
                CompanyName = "CompanyC",
                PortfolioCompanyID = 103
            }
            };
            var imagedata = new Dictionary<int, string> { { 103, "logoC.png" } };
            var columnsDto = new List<ColumnsDto>
            {
            new ColumnsDto
            {
                ID = 12,
                Name = "CustomFieldCol",
                DataType = 1,
                FieldType = 1,
                MapTo = (MapWith)777, // Arbitrary int for custom field
                MapToType = MapToType.CustomFields
            }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, null);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal(string.Empty, row["CustomFieldCol"]);
        }

        // ==================== NEW TESTS FOR LATEST CHANGES ====================

        [Fact]
        public void SerialNo_Constant_ReturnsCorrectValue()
        {
            // Act & Assert
            Assert.Equal("SerialNo", DashboardTrackerHelper.SerialNo);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithDeletedColumns_FiltersOutDeletedTimeSeriesColumns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 2, // TimeSeries
                    DataType = 1,
                    Name = "Revenue",
                    StartPeriod = "Q1 2023",
                    EndPeriod = "Q4 2023",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "Q yyyy",
                    IsPrefix = true,
                    DeletedColumns = "Q2_2023,Q3_2023" // Mark Q2 and Q3 as deleted
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should only have Q1 and Q4 (Q2 and Q3 are deleted)
            
            var timeSeriesIds = result.Select(r => r.TimeSeriesID).ToList();
            Assert.Contains("Q1_2023", timeSeriesIds);
            Assert.Contains("Q4_2023", timeSeriesIds);
            Assert.DoesNotContain("Q2_2023", timeSeriesIds);
            Assert.DoesNotContain("Q3_2023", timeSeriesIds);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithDeletedColumns_FiltersOutDeletedNonTimeSeriesColumns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 1, // Non-TimeSeries
                    DataType = 1,
                    Name = "Revenue",
                    DeletedColumns = "Revenue,EBITDA" // Mark Revenue as deleted
                },
                new DashboardTrackerConfig
                {
                    ID = 2,
                    FieldType = 1, // Non-TimeSeries
                    DataType = 1,
                    Name = "EBITDA",
                    DeletedColumns = "Revenue,EBITDA" // Mark EBITDA as deleted
                },
                new DashboardTrackerConfig
                {
                    ID = 3,
                    FieldType = 1, // Non-TimeSeries
                    DataType = 1,
                    Name = "NetIncome",
                    DeletedColumns = "Revenue,EBITDA" // NetIncome is not deleted
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Should only have NetIncome (Revenue and EBITDA are deleted)
            Assert.Equal("NetIncome", result[0].Name);
            Assert.Equal(3, result[0].ID);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithDeletedColumns_CaseInsensitiveFiltering()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 2, // TimeSeries
                    DataType = 1,
                    Name = "Revenue",
                    StartPeriod = "Q1 2023",
                    EndPeriod = "Q2 2023",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "Q yyyy",
                    IsPrefix = true,
                    DeletedColumns = "Q1_2023" // Exact match, should filter out Q1_2023
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Should only include Q2_2023 since Q1_2023 is deleted
            Assert.Equal("Q2 2023 - Revenue", result[0].Name);
            Assert.Equal("Q2_2023", result[0].TimeSeriesID);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithEmptyDeletedColumns_IncludesAllColumns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 2, // TimeSeries
                    DataType = 1,
                    Name = "Revenue",
                    StartPeriod = "Q1 2023",
                    EndPeriod = "Q2 2023",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "Q yyyy",
                    IsPrefix = true,
                    DeletedColumns = "" // Empty deleted columns
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should include all columns when DeletedColumns is empty
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithNullDeletedColumns_IncludesAllColumns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 2, // TimeSeries
                    DataType = 1,
                    Name = "Revenue",
                    StartPeriod = "Q1 2023",
                    EndPeriod = "Q2 2023",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "Q yyyy",
                    IsPrefix = true,
                    DeletedColumns = null // Null deleted columns
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should include all columns when DeletedColumns is null
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_UsesCustomFieldValues()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto 
                { 
                    ID = 1, 
                    Name = "CustomField", 
                    DataType = 1, 
                    FieldType = 1, 
                    MapTo = MapWith.Website, // This will be ignored due to MapToType
                    MapToType = MapToType.CustomFields,
                    TimeSeriesID = string.Empty 
                }
            };
            var customFieldValues = new List<PageConfigurationFieldValue>
            {
                new PageConfigurationFieldValue
                {
                    PageFeatureId = 1, // Matches PortfolioCompanyID
                    FieldID = (int)MapWith.Website,
                    FieldValue = "Custom Field Value"
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("Custom Field Value", row["CustomField"]); // Should use custom field value, not MapTo value
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_NoMatchingCustomField_FallsBackToMapTo()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://fallback.com"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto 
                { 
                    ID = 1, 
                    Name = "CustomField", 
                    DataType = 1, 
                    FieldType = 1, 
                    MapTo = MapWith.Website,
                    MapToType = MapToType.CustomFields,
                    TimeSeriesID = string.Empty 
                }
            };
            var customFieldValues = new List<PageConfigurationFieldValue>
            {
                new PageConfigurationFieldValue
                {
                    PageFeatureId = 999, // Doesn't match PortfolioCompanyID
                    FieldID = (int)MapWith.Website,
                    FieldValue = "Custom Field Value"
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("http://fallback.com", row["CustomField"]); // Should fall back to MapTo value
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_NoMapToType_UsesMapTo()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://mapto.com"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto 
                { 
                    ID = 1, 
                    Name = "Website", 
                    DataType = 1, 
                    FieldType = 1, 
                    MapTo = MapWith.Website,
                    MapToType = null, // No MapToType specified
                    TimeSeriesID = string.Empty 
                }
            };
            var customFieldValues = new List<PageConfigurationFieldValue>
            {
                new PageConfigurationFieldValue
                {
                    PageFeatureId = 1,
                    FieldID = (int)MapWith.Website,
                    FieldValue = "Custom Field Value"
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("http://mapto.com", row["Website"]); // Should use MapTo value when MapToType is null
        }

        [Fact]
        public void GenerateDashboardTableDataRows_WithCustomFieldValues_EmptyCustomFieldValues_HandlesGracefully()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://mapto.com"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto 
                { 
                    ID = 1, 
                    Name = "Website", 
                    DataType = 1, 
                    FieldType = 1, 
                    MapTo = MapWith.Website,
                    MapToType = MapToType.CustomFields,
                    TimeSeriesID = string.Empty 
                }
            };
            List<PageConfigurationFieldValue> customFieldValues = null; // Null custom field values

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, null, customFieldValues);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("http://mapto.com", row["Website"]); // Should fall back to MapTo value when custom field values are null
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithMapToType_IncludesMapToTypeInColumnsDto()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 1,
                    DataType = 1,
                    Name = "CustomField",
                    MapTo = MapWith.Website,
                    MaptoType = MapToType.CustomFields
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(MapToType.CustomFields, result[0].MapToType);
            Assert.Equal(MapWith.Website, result[0].MapTo);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithDropdownDataType_SetsIsDropdownCorrectly()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 1,
                    DataType = 4, // Dropdown type
                    Name = "DropdownColumn",
                    MapTo = null,
                    MaptoType = null
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.True(result[0].IsDropDown);
            Assert.Equal(4, result[0].DataType);
        }

        [Fact]
        public void GenerateColumnsDtoArray_WithTimeSeriesDropdownDataType_SetsIsDropdownCorrectly()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 2, // TimeSeries
                    DataType = 4, // Dropdown type
                    Name = "TimeSeriesDropdown",
                    StartPeriod = "Q1 2023",
                    EndPeriod = "Q2 2023",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "Q yyyy",
                    IsPrefix = true,
                    MaptoType = null
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Q1 and Q2
            Assert.All(result, column => Assert.True(column.IsDropDown));
            Assert.All(result, column => Assert.Equal(4, column.DataType));
        }

    }
}

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DataAccessLayer.ManagedAccounts
{
    /// <summary>
    /// Entity model for Managed Account Details table
    /// Contains detailed information about managed accounts
    /// </summary>
    [Table("ManagedAccountDetails")]
    public class ManagedAccountDetails
    {
        /// <summary>
        /// Primary key for the managed account details record
        /// </summary>
        [Key]
        public Guid ManagedAccountID { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Name of the managed account
        /// </summary>
        public required string ManagedAccountName { get; set; }

        /// <summary>
        /// Legal domicile of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Domicile { get; set; }

        /// <summary>
        /// Date when the managed account commenced
        /// </summary>
        public DateTime? CommencementDate { get; set; }

        /// <summary>
        /// End date of the investment period (text format)
        /// </summary>
        [StringLength(100)]
        public string? InvestmentPeriodEndDate { get; set; }

        /// <summary>
        /// Maturity date of the managed account
        /// </summary>
        public string? MaturityDate { get; set; }

        /// <summary>
        /// Outstanding commitment amount
        /// </summary>        
        public string? CommitmentOutstanding { get; set; }

        /// <summary>
        /// Currency for the commitment outstanding amount
        /// </summary>
        [StringLength(10)]
        public string? CommitmentOutstandingCurrency { get; set; }

        /// <summary>
        /// Base currency of the managed account
        /// </summary>
        [StringLength(10)]
        public string? BaseCurrency { get; set; }

        /// <summary>
        /// Investment manager for the managed account
        /// </summary>
        [StringLength(200)]
        public string? InvestmentManager { get; set; }

        /// <summary>
        /// Administrator of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Administrator { get; set; }

        /// <summary>
        /// Custodian of the managed account
        /// </summary>
        [StringLength(200)]
        public string? Custodian { get; set; }

        /// <summary>
        /// Legal counsel for the managed account
        /// </summary>
        [StringLength(200)]
        public string? LegalCounsel { get; set; }

        /// <summary>
        /// Legal Entity Identifier (alphanumeric)
        /// </summary>
        [StringLength(50)]
        public string? LEI { get; set; }

        /// <summary>
        /// Managed accounts investment summary details
        /// </summary>
        [StringLength(6000)]
        public string? InvestmentSummary { get; set; }

        // Audit Log Fields

        /// <summary>
        /// Indicates if the record is active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the record is deleted (soft delete)
        /// </summary>
        [Required]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Date and time when the record was created
        /// </summary>
        [Required]
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User ID who created the record
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }

        /// <summary>
        /// Date and time when the record was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }

        /// <summary>
        /// User ID who last modified the record
        /// </summary>
        public int? ModifiedBy { get; set; }
        
    }
}

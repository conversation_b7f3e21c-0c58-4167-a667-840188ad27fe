﻿using Audit.Enums;
using ClosedXML.Excel;
using Contract.CapTable;
using Contract.Funds;
using Contract.PortfolioCompany;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.UnitOfWork;
using DocumentFormat.OpenXml.Spreadsheet;
using Exports.Helpers;
using Shared;
using Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Utility.Helpers;

namespace Exports.Services
{
    public class ConfigHeaderService(IUnitOfWork unitOfWork, IDapperGenericRepository dapper) : IConfigHeaderService
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IDapperGenericRepository _dapper = dapper;

        public enum HeaderType
        {
            Monthly,
            Quarterly,
            Annual
        }

        /// <summary>
        /// A HashSet of integers representing the types of KPI modules related to financials.
        /// This HashSet is used to quickly check if a given KPI module type is a financial type.
        /// </summary>
        /// <remarks>
        /// The HashSet contains the integer values of the following KpiModuleType enum members:
        /// - ProfitAndLoss
        /// - BalanceSheet
        /// - CashFlow
        /// </remarks>
        private readonly HashSet<int> FinancialsModuleTypes = new HashSet<int>
        {
            (int)KpiModuleType.ProfitAndLoss,
            (int)KpiModuleType.BalanceSheet,
            (int)KpiModuleType.CashFlow
        };

        private readonly HashSet<int> CapTableModuleTypes = new HashSet<int>
        {
            (int)KpiModuleType.CapTable1,
            (int)KpiModuleType.CapTable2,
            (int)KpiModuleType.CapTable3,
            (int)KpiModuleType.CapTable4,
            (int)KpiModuleType.CapTable5,
            (int)KpiModuleType.CapTable6,
            (int)KpiModuleType.CapTable7,
            (int)KpiModuleType.CapTable8,
            (int)KpiModuleType.CapTable9,
            (int)KpiModuleType.CapTable10,
        };
        private readonly HashSet<int> OtherCapTableModuleTypes = new HashSet<int>
        {
            (int)KpiModuleType.OtherCapTable1,
            (int)KpiModuleType.OtherCapTable2,
            (int)KpiModuleType.OtherCapTable3,
            (int)KpiModuleType.OtherCapTable4,
            (int)KpiModuleType.OtherCapTable5,
            (int)KpiModuleType.OtherCapTable6,
            (int)KpiModuleType.OtherCapTable7,
            (int)KpiModuleType.OtherCapTable8,
            (int)KpiModuleType.OtherCapTable9,
            (int)KpiModuleType.OtherCapTable10,
        };

        private readonly HashSet<int> CustomAndOtherModuleTypes = new HashSet<int>
        {
            (int)KpiModuleType.CustomTable1,
            (int)KpiModuleType.CustomTable2,
            (int)KpiModuleType.CustomTable3,
            (int)KpiModuleType.CustomTable4,
            (int)KpiModuleType.CustomTable5,
            (int)KpiModuleType.CustomTable6,
            (int)KpiModuleType.CustomTable7,
            (int)KpiModuleType.CustomTable8,
            (int)KpiModuleType.CustomTable9,
            (int)KpiModuleType.OtherKPI1,
            (int)KpiModuleType.OtherKPI2,
            (int)KpiModuleType.OtherKPI3,
            (int)KpiModuleType.OtherKPI4,
            (int)KpiModuleType.OtherKPI5,
            (int)KpiModuleType.OtherKPI6,
            (int)KpiModuleType.OtherKPI7,
            (int)KpiModuleType.OtherKPI8,
            (int)KpiModuleType.OtherKPI9,
            (int)KpiModuleType.OtherKPI10,
            (int)KpiModuleType.OtherKPI11,
            (int)KpiModuleType.OtherKPI12,
            (int)KpiModuleType.OtherKPI13,
            (int)KpiModuleType.OtherKPI14,
            (int)KpiModuleType.OtherKPI15,
        };

        /// <summary>
        /// Retrieves the Excel headers for a given KPI module.
        /// </summary>
        /// <param name="moduleId">The ID of the KPI module.</param>
        /// <returns>A list of strings representing the Excel headers.</returns>
        /// <remarks>
        /// This method first checks if the module ID is in the FinancialsModuleTypes HashSet. If it is, it adds "LineItem" and "Id" to the headers list; otherwise, it adds "KPI" and "Id".
        /// It then retrieves the page configuration name for the module and the corresponding field ID.
        /// It gets all the subsection fields for the given field ID and for each field, it splits the ChartValue into a list of header types.
        /// For each header type, it retrieves the corresponding Excel headers and adds them to the headers list.
        /// </remarks>
        public List<string> GetPageConfigKpiExcelHeaders(int moduleId)
        {
            List<string> excelHeaders = (moduleId <= (int)KpiModuleType.CashFlow || CustomAndOtherModuleTypes.Contains(moduleId) || moduleId >= Constants.FundFinancialsModule) ? [FinancialsModuleTypes.Contains(moduleId) ? "LineItem" : "KPI", "Id"] : [];
            if (!CapTableModuleTypes.Contains(moduleId) && !OtherCapTableModuleTypes.Contains(moduleId))
                excelHeaders.Add($"{Constants.ModuleIdString}-{moduleId}");
            string pageConfigName = string.Empty;
            if (moduleId >= Constants.FundFinancialsModule)
            {
                pageConfigName = _unitOfWork.MFundKpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive == true && x.ModuleId == moduleId)?.PageConfigFieldName;
            }
            else
            {
                pageConfigName = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.ModuleID == moduleId)?.PageConfigFieldName;
            }
            int? fieldId = _unitOfWork.SubPageFieldsRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator || x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs || x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials || x.SubPageID == (int)PageConfigurationSubFeature.CapTable || x.SubPageID == (int)PageConfigurationSubFeature.OtherCapTable || x.SubPageID == (int)PageConfigurationSubFeature.FundKpis || x.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis) && x.Name == pageConfigName)?.FieldID;
            var subSectionFields = _unitOfWork.MSubSectionFieldsRepository.GetManyQueryable(y => !y.IsDeleted && y.FieldID == fieldId)?.OrderBy(field => field.Name != null ? field.Name.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).Length : 0)?.OrderBy(x => x.SectionID).ToList();
            foreach (var field in subSectionFields)
            {
                List<string> mappedTypes = string.IsNullOrEmpty(field.ChartValue) ? [] : field.ChartValue?.Split(',').ToList<string>();
                foreach (var headerType in mappedTypes)
                {
                    string headerKey = FinancialsModuleTypes.Contains(moduleId) ? field.AliasName : field.Name;
                    excelHeaders.AddRange(KpiHeaderHelper.GetExcelHeaders(headerKey, headerType));
                }
                if (mappedTypes?.Count != 0)
                    excelHeaders.Add(string.Empty);
            }
            return excelHeaders;
        }
        public string GetPageConfigName(string pageConfigName)
        {
            var aliasName = _unitOfWork.SubPageFieldsRepository.GetQueryable().FirstOrDefault(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator || x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs) && x.Name == pageConfigName)?.AliasName;
            aliasName = aliasName?[..Math.Min(aliasName.Length, 24)];
            return aliasName;
        }

        /// <summary>
        /// Asynchronously retrieves a list of KPI templates mapped to a given company and module.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of KpiTemplate objects.</returns>
        /// <remarks>
        /// This method uses the Dapper micro-ORM to execute a SQL query that retrieves the KPI templates mapped to the specified company and module.
        /// The SQL query is defined in the SqlConstants.QueryGetMappedKpi constant.
        /// The method passes the company ID and module ID as parameters to the SQL query.
        /// </remarks>
        public async Task<List<KpiTemplate>> GetPortfolioCompanyMappedKpi(int companyId, int moduleId)
        {
            return await _dapper.Query<KpiTemplate>(SqlConstants.QueryGetMappedKpi, new { @ModuleId = moduleId, @CompanyId = companyId });
        }

        /// <summary>
        /// A Dictionary mapping module names to their corresponding KPI module types.
        /// </summary>
        /// <remarks>
        /// The Dictionary contains the following key-value pairs:
        /// - "InvestmentKPI" : KpiModuleType.Investment
        /// - "ImpactKPI" : KpiModuleType.Impact
        /// - "OperationalKPI" : KpiModuleType.Operational
        /// - "CompanyKPI" : KpiModuleType.Company
        /// - "TradingRecords" : KpiModuleType.TradingRecords
        /// - "CreditKPI" : KpiModuleType.CreditKPI
        /// This Dictionary is used to quickly retrieve the KPI module type for a given module name.
        /// </remarks>
        private readonly Dictionary<string, int> ModuleNameByModuleType = new()
        {
            ["InvestmentKPI"] = (int)KpiModuleType.Investment,
            ["ImpactKPI"] = (int)KpiModuleType.Impact,
            ["OperationalKPI"] = (int)KpiModuleType.Operational,
            ["CompanyKPI"] = (int)KpiModuleType.Company,
            ["TradingRecords"] = (int)KpiModuleType.TradingRecords,
            ["CreditKPI"] = (int)KpiModuleType.CreditKPI,

            [Constants.CustomTable1] = (int)KpiModuleType.CustomTable1,
            [Constants.CustomTable2] = (int)KpiModuleType.CustomTable2,
            [Constants.CustomTable3] = (int)KpiModuleType.CustomTable3,
            [Constants.CustomTable4] = (int)KpiModuleType.CustomTable4,
            [Constants.CustomTable5] = (int)KpiModuleType.CustomTable5,
            [Constants.CustomTable6] = (int)KpiModuleType.CustomTable6,
            [Constants.CustomTable7] = (int)KpiModuleType.CustomTable7,
            [Constants.CustomTable8] = (int)KpiModuleType.CustomTable8,
            [Constants.CustomTable9] = (int)KpiModuleType.CustomTable9,
            [Constants.OtherKPI1] = (int)KpiModuleType.OtherKPI1,
            [Constants.OtherKPI2] = (int)KpiModuleType.OtherKPI2,
            [Constants.OtherKPI3] = (int)KpiModuleType.OtherKPI3,
            [Constants.OtherKPI4] = (int)KpiModuleType.OtherKPI4,
            [Constants.OtherKPI5] = (int)KpiModuleType.OtherKPI5,
            [Constants.OtherKPI6] = (int)KpiModuleType.OtherKPI6,
            [Constants.OtherKPI7] = (int)KpiModuleType.OtherKPI7,
            [Constants.OtherKPI8] = (int)KpiModuleType.OtherKPI8,
            [Constants.OtherKPI9] = (int)KpiModuleType.OtherKPI9,
            [Constants.OtherKPI10] = (int)KpiModuleType.OtherKPI10,
            [Constants.OtherKPI11] = (int)KpiModuleType.OtherKPI11,
            [Constants.OtherKPI12] = (int)KpiModuleType.OtherKPI12,
            [Constants.OtherKPI13] = (int)KpiModuleType.OtherKPI13,
            [Constants.OtherKPI14] = (int)KpiModuleType.OtherKPI14,
            [Constants.OtherKPI15] = (int)KpiModuleType.OtherKPI15

        };
        /// <summary>
        /// Represents the type of the module.
        /// </summary>
        public int GetModuleType(string moduleName)
        {
            ModuleNameByModuleType.TryGetValue(moduleName, out int moduleType);
            return moduleType;
        }

        /// <summary>
        /// Asynchronously creates KPI Excel headers for a given module and company, and saves them to a specified template.
        /// </summary>
        /// <param name="moduleName">The name of the KPI module.</param>
        /// <param name="destinationTemplate">The path to the Excel template where the headers will be saved.</param>
        /// <param name="companyId">The ID of the company.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <remarks>
        /// This method first retrieves the module type for the given module name. If the module type is not 0, it proceeds with the following steps:
        /// - Retrieves the dynamic headers for the module.
        /// - Retrieves the list of KPI templates mapped to the company.
        /// - Opens the Excel template and gets the first worksheet.
        /// - Creates the headers in the worksheet.
        /// - Fills the worksheet with data from the mapped KPI templates.
        /// - Sets the zoom scale of the worksheet view to 85% and hides the second column.
        /// - Saves the changes to the Excel workbook.
        /// </remarks>
        public async Task<string> CreateKpiExcelHeaders(string moduleName, string destinationTemplate, int companyId)
        {
            string fileName = null;
            int moduleId = GetModuleType(moduleName);
            if (moduleId != 0)
            {
                PortfolioCompanyDetails compData = await GetCompany(companyId);
                var dynamicHeaders = GetPageConfigKpiExcelHeaders(moduleId);
                var mappedKpiList = await GetPortfolioCompanyMappedKpi(companyId, moduleId).ConfigureAwait(false);
                var xLWorkbook = new XLWorkbook(destinationTemplate);
                var worksheet = xLWorkbook.Worksheets.First();
                KpiHeaderHelper.CreateHeaders(worksheet, dynamicHeaders);
                KpiHeaderHelper.FillData(worksheet, mappedKpiList);
                worksheet.SheetView.ZoomScale = 85;
                worksheet.Column(2).Hide();
                worksheet.Column(3).Hide();
                fileName = GetPageConfigNameOfModule(moduleName, moduleId);

                if (GetModuleTypes().Contains((KpiModuleType)moduleId))
                {
                    worksheet.Name = fileName;
                }
                fileName = $"{compData?.CompanyName}_{fileName}";
                xLWorkbook.Save();
            }
            return fileName;
        }

        /// <summary>
        /// GetPageConfigNameOfModule
        /// </summary>
        /// <param name="moduleName"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        private string GetPageConfigNameOfModule(string moduleName, int moduleId)
        {
            return moduleId switch
            {
                (int)KpiModuleType.Investment => GetPageConfigName(Common.GetDescription(SubFeatureTypes.InvestmentKPIs)),
                (int)KpiModuleType.Impact => GetPageConfigName(Common.GetDescription(SubFeatureTypes.ImpactKPIs)),
                (int)KpiModuleType.Operational => GetPageConfigName(Common.GetDescription(SubFeatureTypes.OperationalKPIs)),
                (int)KpiModuleType.Company => GetPageConfigName(Common.GetDescription(SubFeatureTypes.CompanyKPIs)),
                (int)KpiModuleType.TradingRecords => GetPageConfigName(Common.GetDescription(SubFeatureTypes.TradingRecords)),
                (int)KpiModuleType.CreditKPI => GetPageConfigName(Common.GetDescription(SubFeatureTypes.CreditKPI)),
                (int)KpiModuleType.CustomTable1 => GetPageConfigName(Constants.CustomTable1),
                (int)KpiModuleType.CustomTable2 => GetPageConfigName(Constants.CustomTable2),
                (int)KpiModuleType.CustomTable3 => GetPageConfigName(Constants.CustomTable3),
                (int)KpiModuleType.CustomTable4 => GetPageConfigName(Constants.CustomTable4),
                (int)KpiModuleType.CustomTable5 => GetPageConfigName(Constants.CustomTable5),
                (int)KpiModuleType.CustomTable6 => GetPageConfigName(Constants.CustomTable6),
                (int)KpiModuleType.CustomTable7 => GetPageConfigName(Constants.CustomTable7),
                (int)KpiModuleType.CustomTable8 => GetPageConfigName(Constants.CustomTable8),
                (int)KpiModuleType.CustomTable9 => GetPageConfigName(Constants.CustomTable9),
                (int)KpiModuleType.OtherKPI1 => GetPageConfigName(Constants.OtherKPI1),
                (int)KpiModuleType.OtherKPI2 => GetPageConfigName(Constants.OtherKPI2),
                (int)KpiModuleType.OtherKPI3 => GetPageConfigName(Constants.OtherKPI3),
                (int)KpiModuleType.OtherKPI4 => GetPageConfigName(Constants.OtherKPI4),
                (int)KpiModuleType.OtherKPI5 => GetPageConfigName(Constants.OtherKPI5),
                (int)KpiModuleType.OtherKPI6 => GetPageConfigName(Constants.OtherKPI6),
                (int)KpiModuleType.OtherKPI7 => GetPageConfigName(Constants.OtherKPI7),
                (int)KpiModuleType.OtherKPI8 => GetPageConfigName(Constants.OtherKPI8),
                (int)KpiModuleType.OtherKPI9 => GetPageConfigName(Constants.OtherKPI9),
                (int)KpiModuleType.OtherKPI10 => GetPageConfigName(Constants.OtherKPI10),
                (int)KpiModuleType.OtherKPI11 => GetPageConfigName(Constants.OtherKPI11),
                (int)KpiModuleType.OtherKPI12 => GetPageConfigName(Constants.OtherKPI12),
                (int)KpiModuleType.OtherKPI13 => GetPageConfigName(Constants.OtherKPI13),
                (int)KpiModuleType.OtherKPI14 => GetPageConfigName(Constants.OtherKPI14),
                (int)KpiModuleType.OtherKPI15 => GetPageConfigName(Constants.OtherKPI15),
                _ => GetPageConfigName(moduleName),
            };
        }

        /// <summary>
        /// GetModuleTypes
        /// </summary>
        /// <returns></returns>
        private static KpiModuleType[] GetModuleTypes()
        {
            return
            [
                    KpiModuleType.Investment,
                    KpiModuleType.Impact,
                    KpiModuleType.TradingRecords,
                    KpiModuleType.CreditKPI,
                    KpiModuleType.Company,
                    KpiModuleType.Operational,
                    KpiModuleType.CustomTable1,KpiModuleType.CustomTable2,KpiModuleType.CustomTable3,KpiModuleType.CustomTable4,
                    KpiModuleType.CustomTable5,KpiModuleType.CustomTable6,KpiModuleType.CustomTable7,KpiModuleType.CustomTable8,KpiModuleType.CustomTable9,
                    KpiModuleType.OtherKPI1,KpiModuleType.OtherKPI2, KpiModuleType.OtherKPI3,KpiModuleType.OtherKPI4,KpiModuleType.OtherKPI5,
                    KpiModuleType.OtherKPI6,KpiModuleType.OtherKPI7,KpiModuleType.OtherKPI8,KpiModuleType.OtherKPI9,KpiModuleType.OtherKPI10,
                    KpiModuleType.OtherKPI11,KpiModuleType.OtherKPI12,KpiModuleType.OtherKPI13,KpiModuleType.OtherKPI14,KpiModuleType.OtherKPI15
             ];
        }

        private async Task<PortfolioCompanyDetails> GetCompany(int companyId)
        {
            return await _dapper.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByPCWithID, new { @companyId = companyId });
        }


        /// <summary>
        /// Asynchronously creates a financial Excel template for a given company, and saves it to a specified template.
        /// </summary>
        /// <param name="destinationTemplate">The path to the Excel template where the financial data will be saved.</param>
        /// <param name="companyId">The ID of the company.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <remarks>
        /// This method does the following:
        /// - Defines a mapping between sheet names and KPI module types.
        /// - Retrieves the dynamic headers for each module type and stores them in a dictionary.
        /// - Retrieves the list of KPI templates mapped to the company for each module type and stores them in a dictionary.
        /// - Opens the Excel template and gets the worksheets that match the sheet names in the mapping.
        /// - For each worksheet, it does the following:
        ///   - If the worksheet name is in the dictionary of dynamic headers, it creates the headers in the worksheet.
        ///   - If the worksheet name is in the dictionary of mapped KPI templates, it fills the worksheet with data from the templates.
        ///   - Hides the second column of the worksheet and sets the zoom scale of the worksheet view to 85%.
        /// - Saves the changes to the Excel workbook.
        /// </remarks>
        public async Task<string> CreateFinancialsExcelTemplate(string destinationTemplate, int companyId, int userId)
        {
            List<string> pageConfigActiveSheets = GetActiveCompanyFinancialFieldNames();
            var sheetNameToModuleType = GetSheetNameToModuleType(pageConfigActiveSheets);
            await ValidateFinancialsImportAccess(companyId, userId, sheetNameToModuleType);
            var financialHeaderList = await GetFinancialHeaderList(sheetNameToModuleType);
            var financialKpiList = await GetFinancialKpiList(companyId);
            var xLWorkbook = new XLWorkbook(destinationTemplate);
            var worksheets = xLWorkbook.Worksheets.Where(s => sheetNameToModuleType.ContainsKey(s.Name)).ToList();
            PortfolioCompanyDetails compData = await GetCompany(companyId);
            List<string> activeSheets = [];
            var nameToConstantMap = new Dictionary<string, string> { { "ProfitLoss", Constants.BulkUploadProfitLoss }, { "BalanceSheet", Constants.BulkUploadBalanceSheet }, { "CashFlow", Constants.BulkUploadCashFlow } };
            foreach (IXLWorksheet worksheet in worksheets)
            {
                if (financialHeaderList.TryGetValue(worksheet.Name, out List<string> dynamicHeaders))
                {
                    KpiHeaderHelper.CreateHeaders(worksheet, dynamicHeaders);
                }
                if (financialKpiList.TryGetValue(worksheet.Name, out List<KpiTemplate> mappedKpiList))
                {
                    KpiHeaderHelper.FillData(worksheet, mappedKpiList);
                }

                SetSheetName(activeSheets, worksheet, nameToConstantMap);
                worksheet.Column(2).Hide();
                worksheet.Column(3).Hide();
                worksheet.SheetView.ZoomScale = 85;
            }
            ExcelFileHelper.RemoveInactiveFinancialsWorksheet(xLWorkbook.Worksheets.ToList(), activeSheets);
            xLWorkbook.Save();
            return $"{compData?.CompanyName}_Financials";
        }
        /// <summary>
        /// CreateFundFinancialsAndKpisExcelTemplate
        /// </summary>
        /// <param name="destinationTemplate"></param>
        /// <param name="fundId"></param>
        /// <param name="userId"></param>
        /// <param name="sectionName"></param>
        /// <param name="fundName"></param>
        /// <returns></returns>
        public async Task<string> CreateFundFinancialsAndKpisExcelTemplate(string destinationTemplate, int? fundId, int userId, string sectionName, string fundName)
        {
            int subPageId = sectionName == Constants.FundFinancialsStr ? (int)PageConfigurationSubFeature.FundKpis : (int)PageConfigurationSubFeature.FundKeyKpis;
            List<CapTemplateModule> pageConfigActiveSheets = GetActiveFundKpiFieldNames(subPageId);
            await ValidateFundFinancialsAndKpisImportAccess(fundId, userId, pageConfigActiveSheets);
            var subPageDetail = await _unitOfWork.SubPageDetailsRepository.FindFirstAsync(x => x.SubPageID == subPageId && !x.IsDeleted && x.IsActive);
            var fundFinancialKpiList = GetFundKpiList(fundId?? 0, pageConfigActiveSheets.Select(x => x.ModuleId).ToList());
            var xLWorkbook = new XLWorkbook(destinationTemplate);
            KpiHeaderHelper.OrganizeSheets(xLWorkbook, pageConfigActiveSheets);
            var workSheets = xLWorkbook.Worksheets.Where(s => pageConfigActiveSheets.Select(x => x.Name).Contains(s.Name)).ToList();
            List<MSubSectionFields> subSections = await GetCapTableSections(subPageId);
            ProcessWorkSheet(pageConfigActiveSheets, fundFinancialKpiList, workSheets, null, subSections);
            KpiHeaderHelper.PostProcessWorkSheet(pageConfigActiveSheets, xLWorkbook);
            return $"{fundName}_{subPageDetail.AliasName}";
        }
        private async Task ValidateFundFinancialsAndKpisImportAccess(int? fundId, int userId, List<CapTemplateModule> pageConfigActiveSheets)
        {
            var featuresData = await _unitOfWork.FeatureRepository.FindFirstAsync(x => x.Feature == Constants.Fund);
            var featureId = featuresData.FeatureId;
            var permissions = await _dapper.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetFundSubFeaturePermissions, new { UserId = userId, FundId = fundId, FeatureId = featureId });
            pageConfigActiveSheets.RemoveAll(sheet => !permissions.Any(permission => permission.ModuleId == sheet.ModuleId && permission.CanImport));
        }
        /// <summary>
        /// Validates the financials import access for a given company and user.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="sheetNameToModuleType">A dictionary mapping sheet names to module types.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task ValidateFinancialsImportAccess(int companyId, int userId, Dictionary<string, KpiModuleType> sheetNameToModuleType)
        {
            var featuresData = await _unitOfWork.FeatureRepository.FindFirstAsync(x => x.Feature == Constants.PortfolioCompany);
            var featureId = featuresData.FeatureId;
            var permissions = await _dapper.Query<SubFeatureAccessPermissionsModel>(
                SqlConstants.ProcGetPcSubFeaturePermissions,
                new { UserId = userId, CompanyId = companyId, FeatureId = featureId });

            // Combine filtering for efficiency
            var relevantPermissions = permissions
                .Where(permission => permission.CanImport && (
                    permission.ModuleId == (int)KpiModuleType.ProfitAndLoss ||
                    permission.ModuleId == (int)KpiModuleType.BalanceSheet ||
                    permission.ModuleId == (int)KpiModuleType.CashFlow))
                .ToList();

            // Configuration mapping for clarity and maintainability
            var configMapping = new Dictionary<string, string>
            {
                { Constants.ProfitLossConfigName, Constants.BulkUploadProfitLoss },
                { Constants.BalanceSheetConfigName, Constants.BulkUploadBalanceSheet },
                { Constants.CashflowConfigName, Constants.BulkUploadCashFlow }
            };

            foreach (var (pageConfigName, sheetName) in configMapping)
            {
                if (!relevantPermissions.Any(permission => permission.PageConfigName == pageConfigName))
                {
                    sheetNameToModuleType.Remove(sheetName);
                }
            }
        }

        /// <summary>
        /// SetSheetName
        /// </summary>
        /// <param name="activeSheets"></param>
        /// <param name="worksheet"></param>
        /// <param name="nameToConstantMap"></param>
        private void SetSheetName(List<string> activeSheets, IXLWorksheet worksheet, Dictionary<string, string> nameToConstantMap)
        {
            var moduleName = nameToConstantMap.FirstOrDefault(x => x.Value.Contains(worksheet.Name)).Key;
            string sheetName = _unitOfWork.SubPageFieldsRepository.GetActiveRecords(x => x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials)?.FirstOrDefault(x => x.Name == moduleName)?.AliasName;
            if (sheetName?.Length > Constants.CellValueMaxLength)
                sheetName = sheetName[..Math.Min(sheetName.Length, Constants.CellValueMaxLength)];
            if (sheetName != null && !activeSheets.Contains(sheetName))
            {
                activeSheets.Add(sheetName);
                worksheet.Name = sheetName;
            }
        }

        public async Task<string> CreateCapTableExcelTemplate(string destinationTemplate, int companyId, int userId, bool isOtherCaptable = false)
        {
            int subPageId = isOtherCaptable ? (int)PageConfigurationSubFeature.OtherCapTable : (int)PageConfigurationSubFeature.CapTable;
            List<CapTemplateModule> pageConfigActiveSheets = GetActiveCapTableFieldNames(subPageId);
            await ValidateCapTableImportAccess(companyId, userId, pageConfigActiveSheets);
            var subPageDetail = await _unitOfWork.SubPageDetailsRepository.FindFirstAsync(x => x.SubPageID == subPageId && !x.IsDeleted && x.IsActive);
            var financialKpiList = GetCapTableKpiList(companyId, pageConfigActiveSheets.Select(x => x.ModuleId).ToList());
            var xLWorkbook = new XLWorkbook(destinationTemplate);
            KpiHeaderHelper.OrganizeSheets(xLWorkbook, pageConfigActiveSheets);
            var workSheets = xLWorkbook.Worksheets.Where(s => pageConfigActiveSheets.Select(x => x.Name).Contains(s.Name)).ToList();
            PortfolioCompanyDetails compData = await GetCompany(companyId);
            List<MSubSectionFields> subSections = await GetCapTableSections(subPageId);
            ProcessWorkSheet(pageConfigActiveSheets, financialKpiList, workSheets, compData, subSections);
            KpiHeaderHelper.PostProcessWorkSheet(pageConfigActiveSheets, xLWorkbook);
            return $"{compData?.CompanyName}_{subPageDetail.AliasName}";
        }

        /// <summary>
        /// Validates the access for importing CapTable based on the user's permissions.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="pageConfigActiveSheets">The list of active sheets in the page configuration.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task ValidateCapTableImportAccess(int companyId, int userId, List<CapTemplateModule> pageConfigActiveSheets)
        {
            var featuresData = await _unitOfWork.FeatureRepository.FindFirstAsync(x => x.Feature == Constants.PortfolioCompany);
            var featureId = featuresData.FeatureId;
            var permissions = await _dapper.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetPcSubFeaturePermissions, new { UserId = userId, CompanyId = companyId, FeatureId = featureId });
            pageConfigActiveSheets.RemoveAll(sheet => !permissions.Any(permission => permission.ModuleId == sheet.ModuleId && permission.CanImport));
        }

        /// <summary>
        /// Fills the column data in the worksheet based on the financial KPI list.
        /// </summary>
        /// <param name="worksheet">The worksheet to fill with column data.</param>
        /// <param name="financialKpiList">The list of financial KPIs.</param>
        /// <returns>The index of the filled column.</returns>
        private void ProcessWorkSheet(List<CapTemplateModule> pageConfigActiveSheets, List<KpiTemplate> financialKpiList, List<IXLWorksheet> workSheets, PortfolioCompanyDetails compData, List<MSubSectionFields> subSections)
        {
            foreach (var (worksheet, activeSheet, moduleId) in from IXLWorksheet worksheet in workSheets
                                                               let activeSheet = pageConfigActiveSheets.FirstOrDefault(x => x.Name == worksheet.Name)
                                                               let moduleId = activeSheet?.ModuleId ?? 0
                                                               select (worksheet, activeSheet, moduleId))
            {
                if (compData != null)
                {
                    ProcessSheetByModuleId(financialKpiList, compData, subSections, worksheet, activeSheet, moduleId);
                }
                else
                {
                    ProcessSheetByFundModuleId(financialKpiList, worksheet, activeSheet, moduleId);
                }
            }
        }
        /// <summary>
        /// ProcessSheetByModuleId
        /// </summary>
        /// <param name="financialKpiList"></param>
        /// <param name="compData"></param>
        /// <param name="subSections"></param>
        /// <param name="worksheet"></param>
        /// <param name="activeSheet"></param>
        /// <param name="moduleId"></param>
        private void ProcessSheetByModuleId(List<KpiTemplate> financialKpiList, PortfolioCompanyDetails compData, List<MSubSectionFields> subSections, IXLWorksheet worksheet, CapTemplateModule activeSheet, int moduleId)
        {
            KpiHeaderHelper.FillData(worksheet, financialKpiList.Where(x => x.ModuleId == moduleId && x.KpiTypeId == 1).ToList(), 6);
            var cell2 = worksheet.Cell(5, 2);
            cell2.SetValue($"{Constants.ModuleIdString}-{moduleId}");
            int columnIndex = KpiHeaderHelper.FillColumnData(worksheet, financialKpiList.Where(x => x.ModuleId == moduleId && x.KpiTypeId == 2).ToList());
            var headers = GetPageConfigKpiExcelHeaders(moduleId);
            KpiHeaderHelper.CreateHeaders(worksheet, headers, 5, columnIndex);
            KpiHeaderHelper.SetPeriodValue(subSections, worksheet, activeSheet);
            KpiHeaderHelper.SetHeaderValueAndStyles(compData, worksheet, activeSheet, headers);
        }
        /// <summary>
        /// ProcessSheetByFundModuleId
        /// </summary>
        /// <param name="financialKpiList"></param>
        /// <param name="worksheet"></param>
        /// <param name="activeSheet"></param>
        /// <param name="moduleId"></param>
        private void ProcessSheetByFundModuleId(List<KpiTemplate> financialKpiList, IXLWorksheet worksheet, CapTemplateModule activeSheet, int moduleId)
        {
            var headers = GetPageConfigKpiExcelHeaders(moduleId);
            KpiHeaderHelper.CreateHeaders(worksheet, headers);
            KpiHeaderHelper.FillData(worksheet, financialKpiList.Where(x => x.ModuleId == moduleId).ToList());
            worksheet.SheetView.ZoomScale = 85;
            worksheet.Column(2).Hide();
            worksheet.Column(3).Hide();
            worksheet.Name = activeSheet.AliasName?[..Math.Min(activeSheet.AliasName.Length, 24)];
        }
        public async Task<List<MSubSectionFields>> GetCapTableSections(int subPageId)
        {
            return await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.SubPageID == subPageId && x.IsActive && !string.IsNullOrEmpty(x.ChartValue));
        }
        /// <summary>
        /// Retrieves the names of active fields for the CompanyFinancials subpage.
        /// </summary>
        /// <returns>
        /// A list of strings representing the names of active fields for the CompanyFinancials subpage.
        /// </returns>
        /// <remarks>
        /// This method fetches active records from the SubPageFieldsRepository where the SubPageID matches the ID of the CompanyFinancials subpage.
        /// It then selects the 'Name' property of each record and returns these as a list.
        /// </remarks>
        public List<string> GetActiveCompanyFinancialFieldNames()
        {
            var nameToConstantMap = new Dictionary<string, string>
            {
            { "ProfitLoss", Constants.BulkUploadProfitLoss },
            { "BalanceSheet", Constants.BulkUploadBalanceSheet },
            { "CashFlow", Constants.BulkUploadCashFlow }
            };
            var configurations = _unitOfWork.SubPageFieldsRepository.GetActiveRecords(x => x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials);
            return configurations.Select(x => nameToConstantMap.ContainsKey(x.Name) ? nameToConstantMap[x.Name] : x.Name).ToList();
        }
        public List<CapTemplateModule> GetActiveCapTableFieldNames(int subPageId)
        {
            return (from field in _unitOfWork.SubPageFieldsRepository.GetActiveRecords(x => x.SubPageID == subPageId)
                    join module in _unitOfWork.M_KpiModulesRepository.GetActiveRecords() on field.Name equals module.Name
                    select new CapTemplateModule()
                    {
                        Name = field.Name,
                        ModuleId = module.ModuleID,
                        AliasName = field.AliasName,
                        FieldId = field.FieldID,
                        Order = field.SequenceNo
                    }).ToList();
        }
        public List<CapTemplateModule> GetActiveFundKpiFieldNames(int subPageId)
        {
            return (from field in _unitOfWork.SubPageFieldsRepository.GetActiveRecords(x => x.SubPageID == subPageId)
                    join module in _unitOfWork.MFundKpiModulesRepository.GetActiveRecords() on field.Name equals module.Name
                    select new CapTemplateModule()
                    {
                        Name = field.Name,
                        ModuleId = module.ModuleId,
                        AliasName = field.AliasName,
                        FieldId = field.FieldID,
                        Order = field.SequenceNo
                    }).ToList();
        }

        /// <summary>
        /// Gets the mapping of sheet names to KpiModuleType based on active sheets.
        /// </summary>
        /// <param name="pageConfigActiveSheets">List of active sheet names.</param>
        /// <returns>A dictionary mapping sheet names to KpiModuleType. Only includes entries for active sheets.</returns>
        private Dictionary<string, KpiModuleType> GetSheetNameToModuleType(List<string> pageConfigActiveSheets)
        {
            var sheetNameToModuleType = new Dictionary<string, KpiModuleType>
            {
            { Constants.BulkUploadProfitLoss, KpiModuleType.ProfitAndLoss },
            { Constants.BulkUploadBalanceSheet, KpiModuleType.BalanceSheet },
            { Constants.BulkUploadCashFlow, KpiModuleType.CashFlow }
            };
            return sheetNameToModuleType.Where(x => pageConfigActiveSheets.Contains(x.Key)).ToDictionary(x => x.Key, x => x.Value);
        }

        /// <summary>
        /// Asynchronously gets the financial header list for each sheet.
        /// </summary>
        /// <param name="sheetNameToModuleType">A dictionary mapping sheet names to KpiModuleType.</param>
        /// <returns>A Task representing the asynchronous operation. The result is a dictionary mapping sheet names to a list of headers.</returns>
        private async Task<Dictionary<string, List<string>>> GetFinancialHeaderList(Dictionary<string, KpiModuleType> sheetNameToModuleType)
        {
            var financialHeaderList = new Dictionary<string, List<string>>();
            var tasks = sheetNameToModuleType.Select(async kvp => financialHeaderList[kvp.Key] = GetPageConfigKpiExcelHeaders((int)kvp.Value)).ToArray();
            await Task.WhenAll(tasks).ConfigureAwait(false);
            return financialHeaderList;
        }

        /// <summary>
        /// Asynchronously gets the financial KPI list for a company.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <returns>A Task representing the asynchronous operation. The result is a dictionary mapping sheet names to a list of KpiTemplate.</returns>
        private async Task<Dictionary<string, List<KpiTemplate>>> GetFinancialKpiList(int companyId)
        {
            return new Dictionary<string, List<KpiTemplate>>
            {
            { Constants.BulkUploadProfitLoss, await GetPortfolioCompanyMappedKpi(companyId, (int)KpiModuleType.ProfitAndLoss) },
            { Constants.BulkUploadBalanceSheet, await GetPortfolioCompanyMappedKpi(companyId, (int)KpiModuleType.BalanceSheet) },
            { Constants.BulkUploadCashFlow, await GetPortfolioCompanyMappedKpi(companyId, (int)KpiModuleType.CashFlow) }
            };
        }
        private List<KpiTemplate> GetCapTableKpiList(int companyId, List<int> moduleIds)
        {
            return (from mapping in _unitOfWork.MappingCapTableRepository.GetQueryable()
                    join master in _unitOfWork.MCapTableRepository.GetQueryable() on mapping.KpiId equals master.KpiId
                    where mapping.PortfolioCompanyId == companyId && !mapping.IsDeleted && !master.IsDeleted && moduleIds.Contains(mapping.ModuleId)
                    select new KpiTemplate
                    {
                        LineItem = master.Kpi,
                        Id = master.KpiId,
                        DisplayOrder = mapping.DisplayOrder,
                        IsHeader = mapping.ParentKpiId == null || mapping.ParentKpiId == 0 ? true : master.IsHeader,
                        KpiTypeId = master.KpiTypeId ?? 0,
                        ModuleId = mapping.ModuleId
                    }).OrderBy(x => x.DisplayOrder).ToList();
        }
        private List<KpiTemplate> GetFundKpiList(int fundId, List<int> moduleIds)
        {
            return (from mapping in _unitOfWork.MappingFundSectionKpiRepository.GetQueryable()
                    join master in _unitOfWork.MFundSectionKpiRepository.GetQueryable() on mapping.KpiId equals master.FundSectionKpiId
                    where mapping.FundId == fundId && !mapping.IsDeleted && !master.IsDeleted && moduleIds.Contains(mapping.ModuleId ?? 0)
                    select new KpiTemplate
                    {
                        LineItem = master.Kpi,
                        Id = master.FundSectionKpiId,
                        DisplayOrder = mapping.DisplayOrder,
                        IsHeader = mapping.ParentKpiId == null || mapping.ParentKpiId == 0 || master.IsHeader,
                        ModuleId = mapping.ModuleId ?? 0
                    }).OrderBy(x => x.DisplayOrder).ToList();
        }
    }
}
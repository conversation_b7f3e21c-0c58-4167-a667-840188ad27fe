IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ManagedAccountDetails]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ManagedAccountDetails](
        [ManagedAccountID] [uniqueidentifier] NOT NULL DEFAULT NEWID(),
        [ManagedAccountName] [nvarchar](100) NOT NULL,
        [Domicile] [nvarchar](200) NULL,
        [CommencementDate] [date] NULL,
        [InvestmentPeriodEndDate] [nvarchar](100) NULL,
        [MaturityDate] [nvarchar](100) NULL,
        [CommitmentOutstanding] [nvarchar](100) NULL,
        [CommitmentOutstandingCurrency] [nvarchar](10) NULL,
        [BaseCurrency] [nvarchar](10) NULL,
        [InvestmentManager] [nvarchar](200) NULL,
        [Administrator] [nvarchar](200) NULL,
        [Custodian] [nvarchar](200) NULL,
        [LegalCounsel] [nvarchar](200) NULL,
        [LEI] [nvarchar](50) NULL,
        [InvestmentSummary] [nvarchar] (max) NULL,
        -- Audit Log Fields
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        [CreatedOn] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_ManagedAccountDetails] PRIMARY KEY CLUSTERED 
        (
            [ManagedAccountID] ASC
        )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]
END
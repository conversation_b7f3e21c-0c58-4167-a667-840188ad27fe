-------------- BFB-11410 ----------------
GO
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'TrackerDropdownValue' AND COLUMN_NAME = 'DropdownType'
)
BEGIN
    ALTER TABLE [dbo].[TrackerDropdownValue]
    ADD DropdownType INT CONSTRAINT DF_TrackerDropdownValue_DropdownType DEFAULT 0 WITH VALUES;
END

GO
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'DashboardTrackerConfig' AND COLUMN_NAME = 'MaptoType'
)
BEGIN
    ALTER TABLE [dbo].[DashboardTrackerConfig]
    ADD MaptoType INT CONSTRAINT DF_DashboardTrackerConfig_DropdownType DEFAULT 0 WITH VALUES;
END
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'DashboardTrackerConfig' AND COLUMN_NAME = 'DeletedColumns'
)
BEGIN
    ALTER TABLE [dbo].[DashboardTrackerConfig]
    ADD DeletedColumns VARCHAR(MAX) NULL;
END
GO
﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
	  <PackageReference Include="coverlet.msbuild" Version="6.0.2">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
	  </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageReference Include="xunit" Version="2.5.3" />
	  <PackageReference Include="FluentAssertions" Version="5.7.0" />
	  <PackageReference Include="Moq" Version="4.20.70" />
	  <PackageReference Include="System.Net.Http" Version="4.3.4" />
	  <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
	  <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
	  </PackageReference>

  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\..\src\Account\Account.csproj" />
		<ProjectReference Include="..\..\..\src\API\API.csproj" />
		<ProjectReference Include="..\..\..\src\Audit\Audit.csproj" />
		<ProjectReference Include="..\..\..\src\CashFlow\CashFlow.csproj" />
		<ProjectReference Include="..\..\..\src\Contract\Contract.csproj" />
		<ProjectReference Include="..\..\..\src\Currency\CurrencyRates.csproj" />
		<ProjectReference Include="..\..\..\src\DataAccessLayer\DataAccessLayer.csproj" />
		<ProjectReference Include="..\..\..\src\Deal\Deal.csproj" />
		<ProjectReference Include="..\..\..\src\EmailConfiguration\EmailConfiguration.csproj" />
		<ProjectReference Include="..\..\..\src\Exports\Exports.csproj" />
		<ProjectReference Include="..\..\..\src\Firm\Firm.csproj" />
		<ProjectReference Include="..\..\..\src\Fund\Fund.csproj" />
		<ProjectReference Include="..\..\..\src\Group\Group.csproj" />
		<ProjectReference Include="..\..\..\src\Imports\Imports.csproj" />
		<ProjectReference Include="..\..\..\src\Master\Master.csproj" />
		<ProjectReference Include="..\..\..\src\ManagedAccounts\ManagedAccounts.csproj" />
		<ProjectReference Include="..\..\..\src\Pipeline\Pipeline.csproj" />
		<ProjectReference Include="..\..\..\src\PortfolioCompany\PortfolioCompany.csproj" />
		<ProjectReference Include="..\..\..\src\Report\Report.csproj" />
	</ItemGroup>
</Project>

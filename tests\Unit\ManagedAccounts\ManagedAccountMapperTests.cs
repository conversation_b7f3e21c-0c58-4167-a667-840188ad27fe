using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Mappers;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.UnitTest
{
    public class ManagedAccountMapperTests
    {
        [Fact]
        public void ToResponseDto_ValidEntity_ReturnsCorrectDto()
        {
            // Arrange
            var entity = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Test Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "Test Manager",
                Administrator = "Test Admin",
                Custodian = "Test Custodian",
                LegalCounsel = "Test Legal",
                LEI = "12345678901234567890",
                InvestmentSummary = "Test Summary",
                CreatedOn = DateTime.UtcNow,
                CreatedBy = 1,
                IsActive = true,
                IsDeleted = false
            };

            // Act
            var dto = ManagedAccountMapper.ToResponseDto(entity);

            // Assert
            Assert.NotNull(dto);
            Assert.Equal(entity.ManagedAccountID, dto.Id);
            Assert.Equal(entity.ManagedAccountName, dto.Name);
            Assert.Equal(entity.Domicile, dto.Domicile);
            Assert.Equal(entity.CommencementDate, dto.CommencementDate);
            Assert.Equal(entity.InvestmentPeriodEndDate, dto.InvestmentPeriodEndDate);
            Assert.Equal(entity.MaturityDate, dto.MaturityDate);
            Assert.Equal(entity.CommitmentOutstanding, dto.CommitmentOutstanding);
            Assert.Equal(entity.CommitmentOutstandingCurrency, dto.CommitmentOutstandingCurrency);
            Assert.Equal(entity.BaseCurrency, dto.BaseCurrency);
            Assert.Equal(entity.InvestmentManager, dto.InvestmentManager);
            Assert.Equal(entity.Administrator, dto.Administrator);
            Assert.Equal(entity.Custodian, dto.Custodian);
            Assert.Equal(entity.LegalCounsel, dto.LegalCounsel);
            Assert.Equal(entity.LEI, dto.LEI);
            Assert.Equal(entity.InvestmentSummary, dto.InvestmentSummary);
            Assert.Equal(entity.CreatedOn, dto.CreatedOn);
        }

        [Fact]
        public void ToSummaryDto_ValidEntity_ReturnsCorrectDto()
        {
            // Arrange
            var entity = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Test Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                BaseCurrency = "USD",
                InvestmentManager = "Test Manager",
                CreatedOn = DateTime.UtcNow,
                CreatedBy = 1,
                IsActive = true,
                IsDeleted = false
            };

            // Act
            var dto = ManagedAccountMapper.ToSummaryDto(entity);

            // Assert
            Assert.NotNull(dto);
            Assert.Equal(entity.ManagedAccountID, dto.Id);
            Assert.Equal(entity.ManagedAccountName, dto.Name);
            Assert.Equal(entity.Domicile, dto.Domicile);
            Assert.Equal(entity.CommencementDate, dto.CommencementDate);
            Assert.Equal(entity.BaseCurrency, dto.BaseCurrency);
            Assert.Equal(entity.InvestmentManager, dto.InvestmentManager);
            Assert.Equal(entity.CreatedOn, dto.CreatedOn);
        }

        [Fact]
        public void ToResponseDto_NullEntity_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => ManagedAccountMapper.ToResponseDto(null));
        }

        [Fact]
        public void ToSummaryDto_NullEntity_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => ManagedAccountMapper.ToSummaryDto(null));
        }

        [Fact]
        public void ToResponseDtos_ValidEntities_ReturnsCorrectDtos()
        {
            // Arrange
            var entities = new List<ManagedAccountDetails>
            {
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 1",
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = 1,
                    IsActive = true,
                    IsDeleted = false
                },
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 2",
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = 1,
                    IsActive = true,
                    IsDeleted = false
                }
            };

            // Act
            var dtos = ManagedAccountMapper.ToResponseDtos(entities);

            // Assert
            Assert.NotNull(dtos);
            Assert.Equal(2, dtos.Count());
            Assert.Equal("Account 1", dtos.First().Name);
            Assert.Equal("Account 2", dtos.Last().Name);
        }

        [Fact]
        public void ToSummaryDtos_ValidEntities_ReturnsCorrectDtos()
        {
            // Arrange
            var entities = new List<ManagedAccountDetails>
            {
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 1",
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = 1,
                    IsActive = true,
                    IsDeleted = false
                },
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 2",
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = 1,
                    IsActive = true,
                    IsDeleted = false
                }
            };

            // Act
            var dtos = ManagedAccountMapper.ToSummaryDtos(entities);

            // Assert
            Assert.NotNull(dtos);
            Assert.Equal(2, dtos.Count());
            Assert.Equal("Account 1", dtos.First().Name);
            Assert.Equal("Account 2", dtos.Last().Name);
        }

        [Fact]
        public void ToResponseDtos_NullEntities_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => ManagedAccountMapper.ToResponseDtos(null));
        }

        [Fact]
        public void ToSummaryDtos_NullEntities_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => ManagedAccountMapper.ToSummaryDtos(null));
        }
    }
}

using System.Collections.Generic;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for getting all managed accounts
    /// </summary>
    public class GetAllManagedAccountsResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// The list of managed account summary details
        /// </summary>
        public IEnumerable<ManagedAccountSummaryDto>? Accounts { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="accounts">The list of managed account summary details</param>
        /// <returns>Success result</returns>
        public static GetAllManagedAccountsResult Success(IEnumerable<ManagedAccountSummaryDto> accounts)
        {
            return new GetAllManagedAccountsResult
            {
                IsSuccess = true,
                Accounts = accounts
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <returns>Failure result</returns>
        public static GetAllManagedAccountsResult Failure(string errorMessage)
        {
            return new GetAllManagedAccountsResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}

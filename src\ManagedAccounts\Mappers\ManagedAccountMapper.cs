using System.Collections.Generic;
using System.Linq;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.Mappers
{
    /// <summary>
    /// Mapper for converting ManagedAccountDetails entities to DTOs
    /// </summary>
    public static class ManagedAccountMapper
    {
        /// <summary>
        /// Maps a ManagedAccountDetails entity to ManagedAccountResponseDto
        /// </summary>
        /// <param name="entity">The entity to map</param>
        /// <returns>Mapped DTO</returns>
        public static ManagedAccountResponseDto ToResponseDto(ManagedAccountDetails entity)
        {
            if (entity == null)
                throw new System.ArgumentNullException(nameof(entity));

            return new ManagedAccountResponseDto
            {
                Id = entity.ManagedAccountID,
                Name = entity.ManagedAccountName,
                Domicile = entity.Domicile,
                CommencementDate = entity.CommencementDate,
                InvestmentPeriodEndDate = entity.InvestmentPeriodEndDate,
                MaturityDate = entity.MaturityDate,
                CommitmentOutstanding = entity.CommitmentOutstanding,
                CommitmentOutstandingCurrency = entity.CommitmentOutstandingCurrency,
                BaseCurrency = entity.BaseCurrency,
                InvestmentManager = entity.InvestmentManager,
                Administrator = entity.Administrator,
                Custodian = entity.Custodian,
                LegalCounsel = entity.LegalCounsel,
                LEI = entity.LEI,
                InvestmentSummary = entity.InvestmentSummary,
                CreatedOn = entity.CreatedOn
            };
        }

        /// <summary>
        /// Maps a ManagedAccountDetails entity to ManagedAccountSummaryDto
        /// </summary>
        /// <param name="entity">The entity to map</param>
        /// <returns>Mapped summary DTO</returns>
        public static ManagedAccountSummaryDto ToSummaryDto(ManagedAccountDetails entity)
        {
            if (entity == null)
                throw new System.ArgumentNullException(nameof(entity));

            return new ManagedAccountSummaryDto
            {
                Id = entity.ManagedAccountID,
                Name = entity.ManagedAccountName,
                Domicile = entity.Domicile,
                CommencementDate = entity.CommencementDate,
                BaseCurrency = entity.BaseCurrency,
                InvestmentManager = entity.InvestmentManager,
                CreatedOn = entity.CreatedOn
            };
        }

        /// <summary>
        /// Maps a collection of ManagedAccountDetails entities to ManagedAccountResponseDto collection
        /// </summary>
        /// <param name="entities">The entities to map</param>
        /// <returns>Mapped DTO collection</returns>
        public static IEnumerable<ManagedAccountResponseDto> ToResponseDtos(IEnumerable<ManagedAccountDetails> entities)
        {
            if (entities == null)
                throw new System.ArgumentNullException(nameof(entities));

            return entities.Select(ToResponseDto);
        }

        /// <summary>
        /// Maps a collection of ManagedAccountDetails entities to ManagedAccountSummaryDto collection
        /// </summary>
        /// <param name="entities">The entities to map</param>
        /// <returns>Mapped summary DTO collection</returns>
        public static IEnumerable<ManagedAccountSummaryDto> ToSummaryDtos(IEnumerable<ManagedAccountDetails> entities)
        {
            if (entities == null)
                throw new System.ArgumentNullException(nameof(entities));

            return entities.Select(ToSummaryDto);
        }
    }
}

using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Mappers;

namespace ManagedAccounts.Handlers.Queries
{
    /// <summary>
    /// Handler for getting all managed accounts
    /// </summary>
    public class GetAllManagedAccountsHandler : IRequestHandler<GetAllManagedAccountsQuery, GetAllManagedAccountsResult>
    {
        private readonly IManagedAccountDetailsService _managedAccountService;
        private readonly ILogger<GetAllManagedAccountsHandler> _logger;

        public GetAllManagedAccountsHandler(
            IManagedAccountDetailsService managedAccountService,
            ILogger<GetAllManagedAccountsHandler> logger)
        {
            _managedAccountService = managedAccountService ?? throw new ArgumentNullException(nameof(managedAccountService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get all managed accounts query
        /// </summary>
        /// <param name="request">The get all query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<GetAllManagedAccountsResult> Handle(GetAllManagedAccountsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting all managed accounts");

                var accounts = await _managedAccountService.GetAllAsync();

                _logger.LogInformation("Successfully retrieved all managed accounts");

                var accountDtos = ManagedAccountMapper.ToSummaryDtos(accounts);
                return GetAllManagedAccountsResult.Success(accountDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all managed accounts");
                return GetAllManagedAccountsResult.Failure($"Failed to get managed accounts: {ex.Message}");
            }
        }
    }
}

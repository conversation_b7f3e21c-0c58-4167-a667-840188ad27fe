using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DataAccessLayer.ManagedAccounts;

namespace ManagedAccounts.Interfaces
{
    /// <summary>
    /// Interface for Managed Account Details data access operations
    /// </summary>
    public interface IManagedAccountDetailsService
    {

        /// <summary>
        /// Create new managed account details record
        /// </summary>
        /// <param name="details">The managed account details to create</param>
        /// <returns>The created managed account details ID</returns>
        Task<Guid> CreateAsync(ManagedAccountDetails details);

        /// <summary>
        /// Get managed account details by ID
        /// </summary>
        /// <param name="id">The managed account ID</param>
        /// <returns>The managed account details or null if not found</returns>
        Task<ManagedAccountDetails?> GetByIdAsync(Guid id);

        /// <summary>
        /// Check if a managed account name already exists
        /// </summary>
        /// <param name="managedAccountName">The managed account name to check</param>
        /// <param name="excludeId">Optional ID to exclude from the check (for updates)</param>
        /// <returns>True if the name already exists, false otherwise</returns>
        Task<bool> CheckDuplicateNameAsync(string managedAccountName, Guid? excludeId = null);

        /// <summary>
        /// Get all managed account details
        /// </summary>
        /// <returns>List of all managed account details</returns>
        Task<IEnumerable<ManagedAccountDetails>> GetAllAsync();

    }
}

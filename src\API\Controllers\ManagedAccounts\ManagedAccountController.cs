using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Commands;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using API.Helpers;

namespace API.Controllers.ManagedAccounts
{
    /// <summary>
    /// Controller for managing managed accounts
    /// </summary>
    [Route("api/managed-accounts")]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [ApiController]
    public class ManagedAccountController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IHelperService _userService;
        private readonly ILogger<ManagedAccountController> _logger;

        public ManagedAccountController(
            IMediator mediator,
            IHelperService userService,
            ILogger<ManagedAccountController> logger)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Create a new managed account
        /// </summary>
        /// <param name="command">The managed account creation command</param>
        /// <returns>Created managed account details</returns>
        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> CreateManagedAccount([FromBody] CreateManagedAccountCommand command)
        {
            try
            {
                _logger.LogInformation("Creating new managed account with Name: {ManagedAccountName}", command.ManagedAccountName);

                // Set the created by user ID from the current user context
                command.CreatedBy = _userService.GetCurrentUserId(User);

                var result = await _mediator.Send(command);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successfully created managed account with ID: {Id}", result.Id);
                    return CreatedAtAction(nameof(GetManagedAccount), new { id = result.Id }, new
                    {
                        Id = result.Id,
                        Message = "Managed account created successfully"
                    });
                }

                _logger.LogWarning("Failed to create managed account: {ErrorMessage}", result.ErrorMessage);
                return BadRequest(new
                {
                    Error = result.ErrorMessage
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating managed account with domicile: {Domicile}", command.Domicile);
                return StatusCode(500, new
                {
                    Error = "An unexpected error occurred while creating the managed account"
                });
            }
        }

        /// <summary>
        /// Get a managed account by ID
        /// </summary>
        /// <param name="id">The managed account ID</param>
        /// <returns>Managed account details</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetManagedAccount(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting managed account with ID: {Id}", id);

                var query = new GetManagedAccountQuery { Id = id };
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successfully retrieved managed account with ID: {Id}", id);
                    return Ok(result.Account);
                }

                _logger.LogWarning("Failed to get managed account: {ErrorMessage}", result.ErrorMessage);
                return NotFound(new
                {
                    Error = result.ErrorMessage
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed account with ID: {Id}", id);
                return StatusCode(500, new
                {
                    Error = "An unexpected error occurred while retrieving the managed account"
                });
            }
        }

        /// <summary>
        /// Get all managed accounts
        /// </summary>
        /// <returns>List of all managed accounts</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllManagedAccounts()
        {
            try
            {
                _logger.LogInformation("Getting all managed accounts");

                var query = new GetAllManagedAccountsQuery();
                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successfully retrieved all managed accounts");
                    return Ok(result.Accounts);
                }

                _logger.LogWarning("Failed to get managed accounts: {ErrorMessage}", result.ErrorMessage);
                return BadRequest(new
                {
                    Error = result.ErrorMessage
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all managed accounts");
                return StatusCode(500, new
                {
                    Error = "An unexpected error occurred while retrieving managed accounts"
                });
            }
        }

    }
}

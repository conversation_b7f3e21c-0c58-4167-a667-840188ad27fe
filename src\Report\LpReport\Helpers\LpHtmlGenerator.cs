using Contract.Configuration;
using Contract.Pdf;
using Report.Enums;
using Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Report.LpReport.Helpers
{
    /// <summary>
    /// Static helper class for generating HTML content for LP Reports
    /// </summary>
    public static class LpHtmlGenerator
    {
        /// <summary>
        /// Creates HTML for business description section
        /// </summary>
        /// <param name="staticData">LP report configuration data model</param>
        /// <param name="companyId">Company identifier</param>
        /// <param name="htmlBuilder">StringBuilder instance for HTML generation</param>
        public static void CreateBusinessDescriptionHtml(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder)
        {
            List<PortfolioPageConfigModel> portfolioCompanyDetails = staticData.PortfolioCompanyDetails.Where(x => x.CompanyId == companyId).ToList();
            PageFieldValueModel businessDescription = portfolioCompanyDetails[0]?.PageConfigMappedFields.Find(x => x.Name == Constants.BusinessDescription);
            if (string.IsNullOrEmpty(businessDescription?.DisplayName))
                return;
            string businessDescriptionValue = string.IsNullOrEmpty(businessDescription?.Value) ? HtmlConstants.NoDataFoundHtml : businessDescription.Value;
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfCompanyDetail} businessDescription\">");
            htmlBuilder.AppendLine($"<h4 class=\"{HtmlConstants.PdfH4}\">{businessDescription?.DisplayName}</h4>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfBorderOrange}\"></div>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfP}\">");
            htmlBuilder.AppendLine($"{businessDescriptionValue}");
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
        }

        /// <summary>
        /// Creates HTML for company static details section
        /// </summary>
        /// <param name="staticData">LP report configuration data model</param>
        /// <param name="companyId">Company identifier</param>
        /// <param name="htmlBuilder">StringBuilder instance for HTML generation</param>
        /// <param name="configModel">Mapped report configuration model</param>
        public static void CreateCompanyStaticDetails(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, MappedLpReportConfigModel configModel)
        {
            List<PageFieldValueModel> portfolioCompanyDetails = LpUtilities.GetCompanyFields(staticData, companyId, configModel.KpiIds);
            if (portfolioCompanyDetails.Any())
            {
                htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.SectionHeaderBreak}\">");
                htmlBuilder.AppendLine($"<h4 class=\"{HtmlConstants.PdfH4} {HtmlConstants.DisplayNone}\">{configModel.DisplayTitle}</h4>");
                htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfBorderOrange} {HtmlConstants.DisplayNone}\"></div>");
                htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfLegalDetail}\">");
                portfolioCompanyDetails.ForEach(item =>
                {
                    if (item.Name == Constants.CompanyLegalName)
                    {
                        CreateLegalTradingDetails(item, htmlBuilder);
                    }
                    else
                    {
                        htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfLabel}\">");
                        htmlBuilder.AppendLine(item.DisplayName);
                        htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                        htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfValue}\">");
                        string formattedValue = string.IsNullOrEmpty(item.Value) ? Constants.NotAvailable : LpUtilities.CustomPageConfigFormat(item.Value, item.DataTypeId, item.DataTypeId.GetCustomDecimalPlaces());
                        htmlBuilder.AppendLine(formattedValue);
                        htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                        htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfClearFix}\"></div>");
                    }
                });
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            }
        }

        /// <summary>
        /// Creates HTML for legal and trading details section
        /// </summary>
        /// <param name="item">Page field value model containing legal name details</param>
        /// <param name="htmlBuilder">StringBuilder instance for HTML generation</param>
        public static void CreateLegalTradingDetails(PageFieldValueModel item, StringBuilder htmlBuilder)
        {
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.LegalTradingName}\">");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.LegalName} {HtmlConstants.PdfCol35} {HtmlConstants.PdfLeft} {HtmlConstants.PdfPad5}\">{item.DisplayName}</div>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfLegalDetail} {HtmlConstants.PdfCol55} {HtmlConstants.PdfLeft} {HtmlConstants.PdfPad5}\">{item.Value}</div>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfClearFix}\"></div>");
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
        }

        /// <summary>
        /// Creates HTML for static details sections like geographic locations and investment professionals
        /// </summary>
        /// <param name="staticData">LP report configuration data model</param>
        /// <param name="companyId">Company identifier</param>
        /// <param name="htmlBuilder">StringBuilder instance for HTML generation</param>
        /// <param name="configModel">Mapped report configuration model</param>
        public static void CreateStaticHtmlDetails(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, MappedLpReportConfigModel configModel)
        {
            List<PortfolioPageConfigModel> tableData = configModel.SectionId switch
            {
                (int)LpSection.GeographicLocations => LpUtilities.GetFields(staticData.PortfolioLocationDetails, companyId, configModel.KpiIds),
                (int)LpSection.InvestmentProfessionals => LpUtilities.GetFields(staticData.InvestmentProfessionals, companyId, configModel.KpiIds),
                _ => new List<PortfolioPageConfigModel>()
            };

            htmlBuilder.Append(GenerateTableHtml(tableData, configModel.DisplayTitle));
        }

        /// <summary>
        /// Generates HTML table structure from portfolio data
        /// </summary>
        /// <param name="portfolioData">List of portfolio page configuration models</param>
        /// <param name="tittle">Table title</param>
        /// <returns>Generated HTML table string</returns>
        public static string GenerateTableHtml(List<PortfolioPageConfigModel> portfolioData, string tittle)
        {
            if (portfolioData == null || portfolioData.Count == 0)
            {
                return string.Empty;
            }
            var headers = GenerateHeaders(portfolioData[0].PageConfigMappedFields);           
            return BuildHtmlTable(headers, portfolioData, tittle);
        }

        /// <summary>
        /// Generates header list from page field values
        /// </summary>
        /// <param name="pageFieldValues">List of page field value models</param>
        /// <returns>List of unique headers</returns>
        private static List<PageFieldValueModel> GenerateHeaders(List<PageFieldValueModel> pageFieldValues)
        {
            return pageFieldValues
                .GroupBy(grp => grp.DisplayName)
                .Select(grp => grp.First())
                .ToList();
        }

        /// <summary>
        /// Builds complete HTML table structure with headers and data
        /// </summary>
        /// <param name="headers">List of header fields</param>
        /// <param name="portfolioData">Portfolio configuration data</param>
        /// <param name="tittle">Table title</param>
        /// <returns>Complete HTML table string</returns>
        private static string BuildHtmlTable(List<PageFieldValueModel> headers, List<PortfolioPageConfigModel> portfolioData, string tittle)
        {
            var htmlBuilder = new StringBuilder();
            htmlBuilder.AppendLine($"<div class=\" {HtmlConstants.SectionHeaderBreak} {HtmlConstants.WrapTable} {tittle}\">");
            htmlBuilder.AppendLine($"<h4 class=\"{HtmlConstants.PdfH4}\">{tittle}</h4>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfBorderOrange}\"></div>");
            htmlBuilder.AppendLine($"<table style=\"{HtmlConstants.TableStyle}\" class=\"{HtmlConstants.PdfDataTable}\">");
            htmlBuilder.AppendLine(HtmlConstants.THeadStartTag);
            htmlBuilder.AppendLine(HtmlConstants.TrStartTag);
            for (int headerIndex = 0; headerIndex < headers.Count; headerIndex++)
            {
                htmlBuilder.AppendLine($"<th style=\"{HtmlConstants.HeaderCellStyle}\">{headers[headerIndex].DisplayName}</th>");
            }
            htmlBuilder.AppendLine(HtmlConstants.TrEndTag);
            htmlBuilder.AppendLine(HtmlConstants.THeadEndTag);
            htmlBuilder.AppendLine(HtmlConstants.TBodyStartTag);

            foreach (var portfolio in portfolioData)
            {
                htmlBuilder.AppendLine(HtmlConstants.TrStartTag);
                foreach (var header in headers)
                {
                    var cellValue = portfolio.PageConfigMappedFields.Find(p => p.FieldID == header.FieldID)?.Value ?? string.Empty;
                    htmlBuilder.AppendLine($"<td style=\"{HtmlConstants.DefaultCellStyle}\">{cellValue}</td>");
                }
                htmlBuilder.AppendLine(HtmlConstants.TrEndTag);
            }
            htmlBuilder.AppendLine(HtmlConstants.TBodyEndTag);
            htmlBuilder.AppendLine(HtmlConstants.TableEndTag);
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            return htmlBuilder.ToString();
        }

        /// <summary>
        /// Appends no data found message to HTML builder
        /// </summary>
        /// <param name="htmlBuilder">StringBuilder instance to append to</param>
        public static void AppendNoDataFound(this StringBuilder htmlBuilder)
        {
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.AlignCenterWrapper} {HtmlConstants.Padding4}\">{HtmlConstants.NoDataFound}</div>");
        }

        /// <summary>
        /// Creates KPI table HTML structure with proper currency and unit formatting
        /// </summary>
        /// <param name="staticData">LP report configuration data containing KPI values and settings</param>
        /// <param name="companyId">Identifier of the portfolio company</param>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        /// <param name="configModel">Configuration model containing section and display settings</param>
        public static void CreateKpiTable(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, MappedLpReportConfigModel configModel)
        {
            var lpReportKpiValues=  KpiUtilities.GetFilteredKpiValues(staticData, companyId, configModel);
            lpReportKpiValues = lpReportKpiValues
        		.Where(x => !string.IsNullOrEmpty(x.KPIValue))
        		.ToList();
            LpDataProcessor.UpdateAndFormatKpiValues(lpReportKpiValues, configModel);
            var kpiHeaders = LpDataProcessor.GetKpiDistinctKpiHeaders(lpReportKpiValues,configModel);
            var tableSuffix = staticData.TableHeaderCurrencies.Find(x => x.Id == companyId);
            lpReportKpiValues=lpReportKpiValues.OrderBy(x => x.DisplayOrder).ToList();
            GenerateKpiHtmlTable(lpReportKpiValues, kpiHeaders, htmlBuilder, configModel, tableSuffix,staticData.ClientCode); 
            if (lpReportKpiValues?.Count > 0)
            {
                CreateFootnoteHtmlDetails(staticData, companyId, htmlBuilder, lpReportKpiValues.FirstOrDefault().ModuleId);
            }
        }

        /// <summary>
        /// Generates HTML table for KPI values with proper formatting and currency display
        /// </summary>
        /// <param name="kpiValues">Collection of KPI values to be displayed</param>
        /// <param name="headers">List of column headers for the KPI table</param>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        /// <param name="config">Configuration settings for the report</param>
        /// <param name="kpiTableHeader">Currency information for table headers</param>
        public static void GenerateKpiHtmlTable(List<LpReportKpiValuesModel> kpiValues, List<KpiHeader> headers, StringBuilder htmlBuilder, MappedLpReportConfigModel config, KpiTableHeaderCurrency kpiTableHeader,string clientCode)
        {
            var currencyCode = kpiTableHeader.GetCurrencyCodeByModule(config.ModuleId, clientCode);
            var unitSuffix = LpUtilities.GetUnitSuffix(config.UnitType);
            var formattedText = string.IsNullOrEmpty(unitSuffix) ? $"({currencyCode})" : $"({currencyCode} '{unitSuffix})";
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.SectionHeaderBreak} {HtmlConstants.WrapTable}\">");
            htmlBuilder.AppendLine($"<h4 class=\"{HtmlConstants.PdfH4}\">{config.DisplayTitle} {formattedText}</h4>");
            htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfBorderOrange}\"></div>");
            if (!kpiValues.Any())
            {
                AppendNoDataFound(htmlBuilder);
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                return;
            }
            const int maxColumns = HtmlConstants.MaxColumnsPerTable; // Set the maximum number of columns per table
            var groupedHeaders = LpDataProcessor.GetGroupedHeaders(headers);
            if (groupedHeaders.Count == 1 && headers.Any())
            {
                headers.ForEach(x =>
                {
                    if (Regex.IsMatch(x.HeaderValue, @"\b(YTD|LTM)\b", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(250)))
                    {
                        string aliasName = Regex.Replace(x.AliasName, @"\s*\((.*?)\s*(.*?)\)\s*", " $2 ", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(250)).Trim();
                        x.AliasName = aliasName.Replace(Constants.ActualString,string.Empty).Replace(Constants.BudgetString, string.Empty).Replace(Constants.ForecastString, string.Empty).Replace(Constants.ICString, string.Empty);
                    }
                    else
                        x.AliasName = Regex.Replace(x.AliasName, @"\s*\(.*?\)\s*", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(250)).Trim();
                });
            }
            for (int columnStart = 0; columnStart < headers.Count; columnStart += maxColumns)
            {
                htmlBuilder.AppendLine($"<table style=\"{HtmlConstants.TableStyle} table-layout: fixed;\" class=\"{HtmlConstants.PdfDataTable} {HtmlConstants.PdfKpiTable}\">");
                AppendTableHeader(htmlBuilder, headers.Skip(columnStart).Take(maxColumns).ToList(), kpiTableHeader.FinancialYearEnd);
                AppendTableBody(htmlBuilder, kpiValues, headers.Skip(columnStart).Take(maxColumns).ToList());
                htmlBuilder.AppendLine(HtmlConstants.TableEndTag);
            }
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
        }

        /// <summary>
        /// Appends table header section to HTML
        /// </summary>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        /// <param name="headers">List of KPI headers</param>
        /// <param name="tableFirstCol">First column header text</param>
        private static void AppendTableHeader(StringBuilder htmlBuilder, List<KpiHeader> headers, string tableFirstCol)
        {
            htmlBuilder.AppendLine(HtmlConstants.THeadStartTag);
            htmlBuilder.AppendLine(HtmlConstants.TrStartTag);
            htmlBuilder.AppendLine($"<th>{(string.IsNullOrEmpty(tableFirstCol) ? "" : $"({tableFirstCol})")}</th>");
            foreach (var header in headers)
            {
                htmlBuilder.AppendLine($"<th>{LpUtilities.RemoveValueTypeString(header.AliasName)}</th>");
            }
            htmlBuilder.AppendLine(HtmlConstants.TrEndTag);
            htmlBuilder.AppendLine(HtmlConstants.THeadEndTag);
        }

        /// <summary>
        /// Appends table body section to HTML
        /// </summary>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        /// <param name="kpiValues">List of KPI values</param>
        /// <param name="headers">List of KPI headers</param>
        public static void AppendTableBody(StringBuilder htmlBuilder, List<LpReportKpiValuesModel> kpiValues, List<KpiHeader> headers)
        {
            htmlBuilder.AppendLine(HtmlConstants.TBodyStartTag);
            var kpiGroups = kpiValues.OrderBy(x => x.DisplayOrder).GroupBy(kpi => kpi.KpiId);
            foreach (var kpiGroup in kpiGroups)
            {
                AppendKpiGroupRow(htmlBuilder, kpiGroup, headers);
            }
            htmlBuilder.AppendLine(HtmlConstants.TBodyEndTag);
        }

        private static void AppendKpiGroupRow(StringBuilder htmlBuilder, IGrouping<int, LpReportKpiValuesModel> kpiGroup, List<KpiHeader> headers)
        {
            string kpi = kpiGroup.First().KPI;
            bool isHeader = kpiGroup.First().IsHeader;
            string kpiBoldClass = kpiGroup.First().IsBoldKPI.GetKpiBoldClass();
            string kpiHeaderClass = isHeader.GetKpiBoldClass();
            htmlBuilder.AppendLine(HtmlConstants.TrStartTag);
            htmlBuilder.AppendLine($"<td class=\"{kpiBoldClass} {kpiHeaderClass}\">{kpi}</td>");
            foreach (var header in headers)
            {
                AppendKpiValueCell(htmlBuilder, kpiGroup, header, kpiBoldClass, kpiHeaderClass, isHeader);
            }
            htmlBuilder.AppendLine(HtmlConstants.TrEndTag);
        }

        private static void AppendKpiValueCell(StringBuilder htmlBuilder, IGrouping<int, LpReportKpiValuesModel> kpiGroup, KpiHeader header, string kpiBoldClass, string kpiHeaderClass, bool isHeader)
        {
            string wrapText = string.Empty;
            string kpiValue = isHeader ? string.Empty : Constants.NotAvailable;
            LpReportKpiValuesModel kpiObject = kpiGroup.FirstOrDefault(kpi =>
                kpi.PeriodType == header.PeriodType &&
                kpi.Year == header.Year &&
                kpi.DataType == header.DataType &&
                (header.DataType == Constants.QuarterlyType && kpi.Quarter == header.Quarter ||
                 header.DataType == Constants.MonthlyType && kpi.Month == header.Month ||
                 header.DataType == Constants.YearlyType));
            if (kpiObject != null)
            {
                wrapText = kpiObject.KPIInfo == Constants.KpiInfoText ? HtmlConstants.WrapText : string.Empty;
                kpiValue = kpiObject.IsHeader ? string.Empty : (kpiObject.KPIValue ?? Constants.NotAvailable);
            }
            htmlBuilder.AppendLine($"<td class=\"{wrapText} {kpiBoldClass} {kpiHeaderClass}\">{kpiValue}</td>");
        }

        /// <summary>
        /// Creates HTML for commentary details section
        /// </summary>
        /// <param name="staticData">LP report configuration data</param>
        /// <param name="companyId">Company identifier</param>
        /// <param name="htmlBuilder">StringBuilder instance for HTML generation</param>
        /// <param name="configModel">Mapped report configuration model</param>
        public static void CreateCommentaryHtmlDetails(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, MappedLpReportConfigModel configModel, bool hasMultipleCommentaries=true)
        {
            var commentaryData=staticData.CommentaryDetails.Where(x=>x.CommentarySectionMappingId==configModel.CommentarySectionMappingId&&x.CompanyId== companyId).OrderBy(x=>x.Sequence).ToList();
            commentaryData.ForEach(item =>
            {
                string periodSuffix = GetCommentaryPeriodSuffix(item, configModel.CommentaryPeriod, hasMultipleCommentaries);
                htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.SectionHeaderBreak} {HtmlConstants.PdfMargin10} \">");
                htmlBuilder.AppendLine($"<h4 class=\"{HtmlConstants.PdfH4}\">{item.DisplayName}{periodSuffix}</h4>");
                htmlBuilder.AppendLine($"<div class=\"{HtmlConstants.PdfBorderOrange}\"></div>");
                htmlBuilder.AppendLine($"<div class=\"{(item.HashData ? HtmlConstants.QuillContainerBubble : string.Empty)}\">");
                htmlBuilder.AppendLine($"<div class=\"{(item.HashData ? HtmlConstants.QuillEditor : string.Empty)}\">");
                string updatedHtml = item?.Value?.Replace("<p>", "<div>")
                                                 .Replace("</p>", "</div>");
                htmlBuilder.AppendLine($"{item.Value}");
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            });
        }

        /// <summary>
        /// CreateFootnoteHtmlDetails
        /// </summary>
        /// <param name="staticData"></param>
        /// <param name="companyId"></param>
        /// <param name="htmlBuilder"></param>
        /// <param name="moduleId"></param>
        public static void CreateFootnoteHtmlDetails(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, int moduleId)
        {
            var footnoteData = staticData.LpFootnotes?.FirstOrDefault(x => x.ModuleId == moduleId && x.CompanyId == companyId)?.FootNote;
            if (!string.IsNullOrEmpty(footnoteData))
            {
                htmlBuilder.AppendLine($"<div class=\"pdf-border-blue pdf-mar-10\"></div>");
                htmlBuilder.AppendLine($"<div class=\"pdf-mainDiv pdf-footnote\">");
                htmlBuilder.AppendLine($"<div class=\"footnote-style\">{footnoteData}</div>");
                htmlBuilder.AppendLine($"<div class=\"pdf-clearfix\"></div>");
                htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            }
        }

        /// <summary>
        /// Gets the custom stylesheet path for the report
        /// </summary>
        /// <param name="rootPath">Root directory path</param>
        /// <returns>Full path to the stylesheet</returns>
        public static string GetUserCustomStyleSheet(string rootPath)
        {
            return @"file:///" + rootPath + @"\style\style.css";
        }

        /// <summary>
        /// Appends KPI values in a two-column layout format
        /// </summary>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        /// <param name="leftItems">List of KPI items for the left column</param>
        /// <param name="rightItems">List of KPI items for the right column</param>
        public static void AppendKpiColumnRows(this StringBuilder htmlBuilder, List<LpReportKpiValuesModel> leftItems, List<LpReportKpiValuesModel> rightItems,string convertSuffix=null)
        {
            htmlBuilder.AppendLine($@"<div class='{HtmlConstants.PdfBorderBlue} {HtmlConstants.PdfMargin10} {HtmlConstants.SectionHeaderBreak}'></div>");
            htmlBuilder.AppendLine($@"<div class='{HtmlConstants.PdfMainDiv}'>");
            AppendTableStartColumn(htmlBuilder);
            int loopCount = Math.Max(leftItems.Count, rightItems.Count);
            for (int i = 0; i < loopCount; i++)
            {
                var leftKPI = i < leftItems.Count ? leftItems[i] : null;
                var rightKPI = i < rightItems.Count ? rightItems[i] : null;
                string tdLeft = GenerateKPIHtml(leftKPI,convertSuffix);
                string tdRight = GenerateKPIHtml(rightKPI, convertSuffix);
                htmlBuilder.Append($"<tr>{tdLeft}{tdRight}</tr>");
            }
            htmlBuilder.AppendLine(HtmlConstants.TableEndTag);
            htmlBuilder.AppendLine($@"<div class='{HtmlConstants.PdfClearfix}'></div>");
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
        }

        /// <summary>
        /// Generates HTML markup for a single KPI value cell
        /// </summary>
        /// <param name="kpi">KPI value model to generate HTML for, can be null</param>
        /// <returns>HTML string representing the KPI value cell</returns>
        private static string GenerateKPIHtml(LpReportKpiValuesModel kpi,string convertSuffix)
        {
            if (kpi == null)
                return $"<td class='{HtmlConstants.BlueTextClass}' width='{HtmlConstants.TdWidthLeft}' valign='{HtmlConstants.TdValign}'></td><td width='{HtmlConstants.TdWidthRight}' valign='{HtmlConstants.TdValign}'></td>";
            if (kpi.KPIInfo == Constants.KpiInfoCurrency && !string.IsNullOrEmpty(kpi?.KPIValue) && kpi.KPIValue != Constants.NotAvailable)
            {
                var suffix = string.IsNullOrEmpty(convertSuffix) ? string.Empty : convertSuffix.Replace("n", string.Empty, StringComparison.OrdinalIgnoreCase).ToLower();
                kpi.KPIValue = $"{Constants.KpiInfoCurrency} {kpi.KPIValue}{suffix}";
            }
            return $"<td class='{HtmlConstants.BlueTextClass}' width='{HtmlConstants.TdWidthLeft}' valign='{HtmlConstants.TdValign}'>{kpi?.KPI}{HtmlConstants.Colon}</td><td width='{HtmlConstants.TdWidthRight}' valign='{HtmlConstants.TdValign}'>{kpi?.KPIValue.ToKpiValue()}</td>";
        }

        /// <summary>
        /// Initializes the start of a table structure for KPI columns
        /// </summary>
        /// <param name="htmlBuilder">StringBuilder instance for generating HTML</param>
        public static void AppendTableStartColumn(StringBuilder htmlBuilder)
        {
            htmlBuilder.AppendLine($@"<table width='{HtmlConstants.TableWidth}' cellpadding='{HtmlConstants.CellPadding}' cellspacing='{HtmlConstants.CellSpacing}' class='{HtmlConstants.PdfDataTable}'>");
        }

        /// <summary>
        /// Generates the HTML div for SDG logos.
        /// </summary>
        /// <param name="sdgLogos">The list of SDG logos.</param>
        /// <param name="htmlBuilder">The StringBuilder instance for generating HTML.</param>
        /// <param name="config">The mapped report configuration model.</param>
        public static void GenerateSDGLogoDiv(List<LpSDGLogoModel> sdgLogos, StringBuilder htmlBuilder, MappedLpReportConfigModel config,int company)
        {
            htmlBuilder.AppendLine("<div class=\"impact-kpi-theme\">");
            htmlBuilder.AppendLine("<div class=\"impact-icon-div\">");
            sdgLogos = sdgLogos.Where(x => x.CompanyId== company && config.KpiIds.Split(',').Select(int.Parse).Contains(x.Id)).ToList();
            foreach (var sdgLogo in sdgLogos)
            {
                htmlBuilder.AppendLine($"<img class=\"impact-icons\" id=\"{sdgLogo.Id}\" src=\"{sdgLogo.ImageBase64}\">");
            }
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
            htmlBuilder.AppendLine(HtmlConstants.DivEndTag);
        }
        /// <summary>
        /// Generates the period suffix for commentary headers when applicable
        /// </summary>
        /// <param name="commentaryItem">The commentary item to generate a period suffix for</param>
        /// <param name="commentaryPeriod">The commentary period setting</param>
        /// <param name="hasMultipleCommentaries">Whether there are multiple commentaries to display</param>
        /// <returns>Formatted period suffix string or empty string when not applicable</returns>
        public static string GetCommentaryPeriodSuffix(LpCommentaryModel commentaryItem, string commentaryPeriod, bool hasMultipleCommentaries)
        {
            if (hasMultipleCommentaries && commentaryItem.HashData)
            {
                return $" - {LpDataProcessor.GetLpCommentaryPeriod(commentaryItem, commentaryPeriod)}";
            }

            return string.Empty;
        }
    }
}
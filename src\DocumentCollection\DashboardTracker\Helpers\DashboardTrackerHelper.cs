﻿using Contract.PortfolioCompany;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.DashboardTracker;
using DocumentCollection.DashboardTracker.DTOs;
namespace DocumentCollection.DashboardTracker.Helpers
{
    public static class DashboardTrackerHelper
    {

        public const string FundId = "FundID";
        public const string PortfolioCompanyId = "PCID";
        public const string PortfolioCompanyName = "Portfolio Company";
        public const string FundName = "Fund";
        public const string LogoColumn = "CompanyLogo";
        public const string SerialNo = "SerialNo";  
        /// <summary>
        /// Generates the data rows for the dashboard table, including static and dynamic columns.
        /// </summary>
        /// <param name="companies">Portfolio companies</param>
        /// <param name="imagedata">Dictionary of company logos</param>
        /// <param name="columnsDto">Dynamic columns</param>
        /// <param name="dropdownValuesDict">Dropdown values for columns</param>
        /// <returns>List of data rows as dictionaries</returns>
        public static List<Dictionary<string, object>> GenerateDashboardTableDataRows(
            IEnumerable<PortfolioCompanyQueryModel> companies,
            Dictionary<int, string> imagedata,
            List<ColumnsDto> columnsDto,           
            IEnumerable<DashboardTrackerCellValue> cellValues = null,
            IEnumerable<PageConfigurationFieldValue> customFieldValues = null)
        {
            var data = new List<Dictionary<string, object>>();
            var cellValueLookup = cellValues?.ToLookup(cv => new { cv.PortfolioCompanyId, cv.ColumnId, cv.TimeSeriesID });
            var customFieldLookup = customFieldValues?.ToLookup(cf => new { cf.PageFeatureId, cf.FieldID });

            foreach (var c in companies)
            {
                var dict = new Dictionary<string, object>
                {
                    [FundId] = c.FundId,
                    [PortfolioCompanyId] = c.PortfolioCompanyID,
                    [FundName] = c.FundName,
                    [PortfolioCompanyName] = c.CompanyName,
                    [LogoColumn] = imagedata.ContainsKey(c.PortfolioCompanyID) ? imagedata[c.PortfolioCompanyID] : string.Empty,
                };

                foreach (var col in columnsDto)
                {
                    object cellValue = string.Empty;

                    // Check if there's a stored cell value for this combination
                    if (cellValueLookup != null)
                    {
                        var storedValue = cellValueLookup[new { PortfolioCompanyId = c.PortfolioCompanyID, ColumnId = col.ID, TimeSeriesID = col.TimeSeriesID }]
                            .FirstOrDefault();
                        if (storedValue != null)
                        {
                            cellValue = storedValue.CellValue ?? string.Empty;
                        }
                    }

                    // If no stored value found, use default logic based on MaptoType
                    if (cellValue.Equals(string.Empty) && col.MapTo.HasValue)
                    {
                        if (col.MapToType == MapToType.CustomFields)
                        {
                            // Get custom field value
                            var customFieldValue = customFieldLookup?[new { PageFeatureId = c.PortfolioCompanyID, FieldID = (int)col.MapTo }]
                                .FirstOrDefault();
                            cellValue = customFieldValue?.FieldValue ?? string.Empty;
                            
                            // If no custom field value found, fall back to MapTo value
                            if (string.IsNullOrEmpty(cellValue.ToString()))
                            {
                                cellValue = GetMaptoColumnData(c, col);
                            }
                        }
                        else
                        {
                            // Use existing static field mapping
                            cellValue = GetMaptoColumnData(c, col);
                        }
                    }

                    dict[col.Name] = cellValue;
                }
                data.Add(dict);
            }
            return data;
        }

        /// <summary>
        /// fill value for mapto column data from the respective variable
        /// </summary>
        /// <param name="c"></param>
        /// <param name="dict"></param>
        /// <param name="col"></param>
        private static void FillMaptoColumnData(PortfolioCompanyQueryModel c, Dictionary<string, object> dict, ColumnsDto col)
        {
            switch (col.MapTo)
            {
                case MapWith.Website:
                    dict[col.Name] = c.Website;
                    break;
                case MapWith.Currency:
                    dict[col.Name] = c.ReportingCurrencyDetail.Currency;
                    break;
                case MapWith.MasterCompanyName:
                    dict[col.Name] = c.MasterCompanyName;
                    break;
                case MapWith.FinancialYearEnd:
                    dict[col.Name] = c.FinancialYearEnd;
                    break;
                case MapWith.CompanyLegalName:
                    dict[col.Name] = c.CompanyLegalName;
                    break;
            }
        }

        /// <summary>
        /// Get value for mapto column data from the respective variable
        /// </summary>
        /// <param name="c"></param>
        /// <param name="col"></param>
        /// <returns></returns>
        private static string GetMaptoColumnData(PortfolioCompanyQueryModel c, ColumnsDto col)
        {
            return col.MapTo switch
            {
                MapWith.Website => c.Website,
                MapWith.Currency => c.ReportingCurrencyDetail?.Currency,
                MapWith.MasterCompanyName => c.MasterCompanyName,
                MapWith.FinancialYearEnd => c.FinancialYearEnd,
                MapWith.CompanyLegalName => c.CompanyLegalName,
                _ => string.Empty
            };
        }

        /// <summary>
        /// Generates an array of ColumnsDto objects for each item in IEnumerable of DashboardTrackerConfig.
        /// For FieldType = 2 (timeseries), creates multiple columns based on StartPeriod and EndPeriod with FrequencyType.
        /// </summary>
        /// <param name="dashboardTrackerConfigs">IEnumerable of DashboardTrackerConfig items</param>
        /// <returns>Array of ColumnsDto objects</returns>
        public static List<ColumnsDto> GenerateColumnsDtoArray(IEnumerable<DashboardTrackerConfig> dashboardTrackerConfigs)
        {
            var columnsList = new List<ColumnsDto>();

            foreach (var config in dashboardTrackerConfigs)
            {
                if (config.FieldType == 2) // TimeSeries
                {
                    // Generate multiple columns based on StartPeriod, EndPeriod, and FrequencyType
                    var timeSeriesColumns = GenerateTimeSeriesColumns(config);
                    
                    // Filter out columns that are already marked as deleted
                    if (!string.IsNullOrEmpty(config.DeletedColumns))
                    {
                        var deletedColumnIds = config.DeletedColumns.Split(',').ToList();
                        timeSeriesColumns = timeSeriesColumns
                            .Where(col => !deletedColumnIds.Contains(col.TimeSeriesID, StringComparer.OrdinalIgnoreCase))
                            .ToList();
                    }
                    
                    columnsList.AddRange(timeSeriesColumns);
                }
                else
                {
                    // Check if this column is already marked as deleted
                    if (!string.IsNullOrEmpty(config.DeletedColumns))
                    {
                        var deletedColumnNames = config.DeletedColumns.Split(',').ToList();
                        if (deletedColumnNames.Contains(config.Name, StringComparer.OrdinalIgnoreCase))
                        {
                            continue; // Skip this column as it's marked as deleted
                        }
                    }
                    
                    // Generate single column for non-timeseries fields
                    var column = new ColumnsDto
                    {
                        ID = config.ID,
                        FieldType = config.FieldType,
                        DataType = config.DataType,
                        Name = config.Name,
                        IsTimeSeries = false,
                        TimeSeriesID = null,
                        IsDropDown = config.DataType == 4,
                        MapTo = config.MapTo,
                        MapToType = config.MaptoType
                    };
                    columnsList.Add(column);
                }
            }

            return columnsList;
        }

        /// <summary>
        /// Generates time series columns based on StartPeriod, EndPeriod, and FrequencyType.
        /// FrequencyType: 1=Monthly, 2=Quarterly, 3=Yearly
        /// </summary>
        /// <param name="config">DashboardTrackerConfig with timeseries data</param>
        /// <returns>List of ColumnsDto for time series</returns>
        public static List<ColumnsDto> GenerateTimeSeriesColumns(DashboardTrackerConfig config)
        {
            var timeSeriesColumns = new List<ColumnsDto>();

            if (string.IsNullOrEmpty(config.StartPeriod) || string.IsNullOrEmpty(config.EndPeriod) || !config.FrequencyType.HasValue || config.TimeSeriesDateFormat == null || !config.IsPrefix.HasValue)
            {
                return timeSeriesColumns;
            }

            try
            {
                var periodTexts = GeneratePeriodTexts(config.StartPeriod, config.EndPeriod, config.FrequencyType.Value, config.TimeSeriesDateFormat);

                for (int i = 0; i < periodTexts.Count; i++)
                {
                    var column = new ColumnsDto
                    {
                        ID = config.ID,
                        FieldType = config.FieldType,
                        DataType = config.DataType,
                        Name = (bool)config.IsPrefix ? $"{periodTexts[i].DisplayText} - {config.Name}" : $"{config.Name} - {periodTexts[i].DisplayText}",
                        IsTimeSeries = true,
                        TimeSeriesID = periodTexts[i].Value,
                        IsDropDown = config.DataType == 4
                    };
                    timeSeriesColumns.Add(column);
                }
            }
            catch (Exception)
            {
                // If period parsing fails, return empty list
                return timeSeriesColumns;
            }

            return timeSeriesColumns;
        }

        /// <summary>
        /// Generates period text representations based on FrequencyType.
        /// </summary>
        /// <param name="startPeriod">Start period string</param>
        /// <param name="endPeriod">End period string</param>
        /// <param name="frequencyType">1=Monthly, 2=Quarterly, 3=Yearly</param>
        /// <returns>List of period text representations</returns>
        public static List<ColumnAlias> GeneratePeriodTexts(string startPeriod, string endPeriod, int frequencyType, string format)
        {
            var periodTexts = new List<ColumnAlias>();

            switch (frequencyType)
            {
                case 1: // Monthly
                    periodTexts = GenerateMonthlyPeriods(startPeriod, endPeriod, format);
                    break;
                case 2: // Quarterly
                    periodTexts = GenerateQuarterlyPeriods(startPeriod, endPeriod, format);
                    break;
                case 3: // Yearly
                    periodTexts = GenerateYearlyPeriods(startPeriod, endPeriod, format);
                    break;
                default:
                    break;
            }

            return periodTexts;
        }

        /// <summary>
        /// Generates monthly period texts (e.g., "Jan 2023", "Feb 2023", etc.)
        /// </summary>
        public static List<ColumnAlias> GenerateMonthlyPeriods(string startPeriod, string endPeriod, string format)
        {
            var periods = new List<ColumnAlias>();

            if (DateTime.TryParse($"01/{startPeriod}", out DateTime startDate) &&
                DateTime.TryParse($"01/{endPeriod}", out DateTime endDate))
            {
                var current = startDate;
                while (current <= endDate)
                {
                    periods.Add(new ColumnAlias() { DisplayText = current.ToString(format), Value = current.ToString("MM_yyyy") });
                    current = current.AddMonths(1);
                }
            }

            return periods;
        }

        /// <summary>
        /// Generates quarterly period texts (e.g., "Q1 2023", "Q2 2023", etc.)
        /// </summary>
        public static List<ColumnAlias> GenerateQuarterlyPeriods(string startPeriod, string endPeriod, string format)
        {
            var periods = new List<ColumnAlias>();

            if (TryParseQuarter(startPeriod, out int startYear, out int startQuarter) &&
                TryParseQuarter(endPeriod, out int endYear, out int endQuarter))
            {
                var currentYear = startYear;
                var currentQuarter = startQuarter;

                AddFormattedPeriod(format, periods, endYear, endQuarter, ref currentYear, ref currentQuarter);
            }

            return periods;
        }

        private static void AddFormattedPeriod(string format, List<ColumnAlias> periods, int endYear, int endQuarter, ref int currentYear, ref int currentQuarter)
        {
            string quarterText = format.ToLower().Contains("quarter") ? "Quarter " : "Q";
            int substringIndex = format.ToLower().Contains("yyyy") ? 0 : 2;
            string slashString = format.Contains("/") ? "/" : " ";
            while (currentYear < endYear || (currentYear == endYear && currentQuarter <= endQuarter))
            {
                string yearstring = format.ToLower().Contains("y") ? currentYear.ToString().Substring(substringIndex) : "";
                periods.Add(new ColumnAlias() { DisplayText = $"{quarterText}{currentQuarter}{slashString}{yearstring}", Value = "Q" + currentQuarter + "_" + currentYear });

                currentQuarter++;
                if (currentQuarter > 4)
                {
                    currentQuarter = 1;
                    currentYear++;
                }
            }
        }

        /// <summary>
        /// Generates yearly period texts (e.g., "2023", "2024", etc.)
        /// </summary>
        public static List<ColumnAlias> GenerateYearlyPeriods(string startPeriod, string endPeriod, string format)
        {
            var periods = new List<ColumnAlias>();
            int substringIndex = format.ToLower().Contains("yyyy") ? 0 : 2;
            if (int.TryParse(startPeriod, out int startYear) &&
                int.TryParse(endPeriod, out int endYear))
            {
                for (int year = startYear; year <= endYear; year++)
                {
                    periods.Add(new ColumnAlias() { DisplayText = year.ToString().Substring(substringIndex), Value = year.ToString() });
                }
            }

            return periods;
        }

        /// <summary>
        /// Tries to parse quarter string (e.g., "Q1 2023" or "2023 Q1")
        /// </summary>
        public static bool TryParseQuarter(string quarterString, out int year, out int quarter)
        {
            year = 0;
            quarter = 0;

            if (string.IsNullOrEmpty(quarterString))
                return false;

            var parts = quarterString.Trim().Split(' ');
            if (parts.Length != 2)
                return false;

            // Try "Q1 2023" format
            if (parts[0].StartsWith('Q') && int.TryParse(parts[0][1..], out quarter) &&
                int.TryParse(parts[1], out year))
            {
                return quarter >= 1 && quarter <= 4;
            }

            // Try "2023 Q1" format
            if (int.TryParse(parts[0], out year) && parts[1].StartsWith('Q') &&
                int.TryParse(parts[1][1..], out quarter))
            {
                return quarter >= 1 && quarter <= 4;
            }

            return false;
        }
    }
}

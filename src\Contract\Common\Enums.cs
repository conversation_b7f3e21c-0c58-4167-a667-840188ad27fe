﻿
using System.ComponentModel;

namespace Contract.Common
{
    public enum KpiModuleType
    {
        [Description("TradingRecords")]
        TradingRecords = 1,
        [Description("CreditKPI")]
        CreditKPI = 2,
        [Description("Operational")]
        Operational = 3,
        [Description("Investment")]
        Investment = 4,
        [Description("Company")]
        Company = 5,
        [Description("Impact")]
        Impact = 6,
        [Description("ProfitAndLoss")]
        ProfitAndLoss = 7,
        [Description("BalanceSheet")]
        BalanceSheet = 8,
        [Description("CashFlow")]
        CashFlow = 9,
        CapTable1 = 11,
        CapTable2 = 12,
        CapTable3 = 13,
        CapTable4 = 14,
        CapTable5 = 15,
        MonthlyReport = 16,
        [Description("CustomTable1")]
        CustomTable1 = 17,
        [Description("CustomTable2")]
        CustomTable2 = 18,
        [Description("CustomTable3")]
        CustomTable3 = 19,
        [Description("CustomTable4")]
        CustomTable4 = 20,
        [Description("OtherKPI1")]
        OtherKPI1 = 21,
        [Description("OtherKPI2")]
        OtherKPI2 = 22,
        [Description("OtherKPI3")]
        OtherKPI3 = 23,
        [Description("OtherKPI4")]
        OtherKPI4 = 24,
        [Description("OtherKPI5")]
        OtherKPI5 = 25,
        [Description("OtherKPI6")]
        OtherKPI6 = 26,
        [Description("OtherKPI7")]
        OtherKPI7 = 27,
        [Description("OtherKPI8")]
        OtherKPI8 = 28,
        [Description("OtherKPI9")]
        OtherKPI9 = 29,
        [Description("OtherKPI10")]
        OtherKPI10 = 30,
        CapTable6 = 31,
        CapTable7 = 32,
        CapTable8 = 33,
        CapTable9 = 34,
        CapTable10 = 35,
        OtherCapTable1 = 36,
        OtherCapTable2 = 37,
        OtherCapTable3 = 38,
        OtherCapTable4 = 39,
        OtherCapTable5 = 40,
        OtherCapTable6 = 41,
        OtherCapTable7 = 42,
        OtherCapTable8 = 43,
        OtherCapTable9 = 44,
        OtherCapTable10 = 45,
        CustomTable5 = 46,
        CustomTable6 = 47,
        CustomTable7 = 48,
        CustomTable8 = 49,
        CustomTable9 = 50,
        OtherKPI11 = 51,
        OtherKPI12 = 52,
        OtherKPI13 = 53,
        OtherKPI14 = 54,
        OtherKPI15 = 55
    }

}

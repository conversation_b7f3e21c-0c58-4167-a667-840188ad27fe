using ManagedAccounts.Models.Results;
using MediatR;

namespace ManagedAccounts.Models.Queries
{
    /// <summary>
    /// Query for getting all managed accounts
    /// </summary>
    public class GetAllManagedAccountsQuery : IRequest<GetAllManagedAccountsResult>
    {
        // No parameters needed for getting all accounts
        // Future enhancements could include filtering, sorting, pagination parameters
    }
}

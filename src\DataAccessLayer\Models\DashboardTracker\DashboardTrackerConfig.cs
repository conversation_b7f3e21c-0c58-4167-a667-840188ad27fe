using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.DashboardTracker
{
    [ExcludeFromCodeCoverage]
    [Table("DashboardTrackerConfig")]
    public class DashboardTrackerConfig : BaseCommonModel
    {
        [Key]
        public int ID { get; set; }

        [Required]
        /// <summary>
        ///  Stores DataType type enum as-- 1=Data, 2=TimeSeries
        /// </summary>
        public int FieldType { get; set; }

        [Required]
        /// <summary>
        /// Stores DataType type enum as -- 1=Text, 2=Number, 3=Date, 4=DropDown
        /// </summary>
        public int DataType { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; }

        /// <summary>
        /// Stores Frequncy type enum as -- 1=Monthly, 2=Quarterly, 3=Annual
        /// </summary>
        public int? FrequencyType { get; set; }

        [MaxLength(50)]
        public string StartPeriod { get; set; }

        [MaxLength(50)]
        public string EndPeriod { get; set; }

        public bool? IsPrefix { get; set; }
        /// <summary>
        /// Stores the TimeSeries, date format
        /// </summary>
        public string? TimeSeriesDateFormat { get; set; }
        /// <summary>
        /// stores Mapto Type StaticFields = 0 | CustomFields = 2
        /// </summary>
        public MapToType? MaptoType { get; set; }

        /// <summary>
        /// Stores the enum id for staticfields or custom field id for Custom fields
        /// </summary>
        public MapWith? MapTo { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;
        public string DeletedColumns { get; set; }

    }    

    public enum MapToType 
    {
        StaticFields = 1,
        CustomFields = 2
    }
    public enum MapWith
    {
        Website = 1,
        Currency = 2,
        MasterCompanyName = 3,
        FinancialYearEnd = 4,
        CompanyLegalName = 5,
    }
}

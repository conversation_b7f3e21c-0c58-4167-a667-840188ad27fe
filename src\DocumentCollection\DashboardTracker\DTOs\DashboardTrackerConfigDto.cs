﻿using CLO.CQRS.Results;
using DataAccessLayer.Models.DashboardTracker;
using System;
using System.Diagnostics.CodeAnalysis;

namespace DocumentCollection.DashboardTracker.DTOs
{
    [ExcludeFromCodeCoverage]
    public class DashboardTrackerConfigDto
    {
        public int? ID { get; set; } // Nullable for insert
        public int FieldType { get; set; }
        public int DataType { get; set; }
        public string Name { get; set; } = "Custom Column";
        public int? FrequencyType { get; set; }
        public string? StartPeriod { get; set; }
        public string? EndPeriod { get; set; }
        public bool? IsPrefix { get; set; }
        public string? TimeSeriesDateFormat { get; set; }
        public int? MapTo { get; set; } = null; // Default to null (no mapping)
        public int? MapToType { get; set; } = null;
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public DateTime? CreatedOn { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public int? ModifiedBy { get; set; }
        public List<string>? DropdownList { get; set; }
        public string? DataTypeName { get; set; }
        public string? FieldTypeName { get; set; }
        public string? DeletedColumns { get; set; }
    }

    public class ColumnsDto
    {
        public int ID { get; set; }
        public int FieldType { get; set; }
        public int DataType { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsTimeSeries { get; set; }
        public string? TimeSeriesID { get; set; } 
        public bool IsDropDown { get; set; }
        public MapWith? MapTo { get; set; }
        public MapToType? MapToType { get; set; }
        public List<ColumnAlias>? DropDownValues {  get; set; }
    }

    public class TableDataResultDto : BaseResult
    {
        public required List<Dictionary<string, object>> Data { get; set; }
        public required List<ColumnsDto> Columns { get; set; }
        public int TotalRecords { get; set; }
    }
    
}
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using ManagedAccounts.Handlers.Commands;
using ManagedAccounts.Models.Commands;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.UnitTest
{
    public class CreateManagedAccountHandlerTests
    {
        private readonly Mock<IManagedAccountDetailsService> _mockService;
        private readonly Mock<ILogger<CreateManagedAccountHandler>> _mockLogger;
        private readonly CreateManagedAccountHandler _handler;

        public CreateManagedAccountHandlerTests()
        {
            _mockService = new Mock<IManagedAccountDetailsService>();
            _mockLogger = new Mock<ILogger<CreateManagedAccountHandler>>();
            _handler = new CreateManagedAccountHandler(_mockService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidCommand_ReturnsSuccessResult()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Managed Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "ABC Investment Management",
                Administrator = "XYZ Administration Services",
                Custodian = "DEF Custody Bank",
                LegalCounsel = "GHI Legal Services",
                LEI = "12345678901234567890",
                InvestmentSummary = "Investment in technology sector companies",
                CreatedBy = 123
            };

            var expectedId = Guid.NewGuid();
            _mockService.Setup(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null))
                .ReturnsAsync(false);
            _mockService.Setup(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()))
                .ReturnsAsync(expectedId);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(expectedId, result.Id);
            Assert.Null(result.ErrorMessage);

            _mockService.Verify(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null), Times.Once);
            _mockService.Verify(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()), Times.Once);
        }

        [Fact]
        public async Task Handle_DuplicateName_ReturnsFailureResult()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Existing Account",
                Domicile = "United States",
                CreatedBy = 123
            };

            _mockService.Setup(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null))
                .ReturnsAsync(true);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Id);
            Assert.Contains("already exists", result.ErrorMessage);
            Assert.Contains(command.ManagedAccountName, result.ErrorMessage);

            _mockService.Verify(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null), Times.Once);
            _mockService.Verify(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ServiceThrowsException_ReturnsFailureResult()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Account",
                Domicile = "United States",
                CreatedBy = 123
            };

            _mockService.Setup(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null))
                .ReturnsAsync(false);
            var exceptionMessage = "Database connection failed";
            _mockService.Setup(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Id);
            Assert.Contains(exceptionMessage, result.ErrorMessage);

            _mockService.Verify(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null), Times.Once);
            _mockService.Verify(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ValidCommand_MapsPropertiesCorrectly()
        {
            // Arrange
            var command = new CreateManagedAccountCommand
            {
                ManagedAccountName = "Test Managed Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "ABC Investment Management",
                Administrator = "XYZ Administration Services",
                Custodian = "DEF Custody Bank",
                LegalCounsel = "GHI Legal Services",
                LEI = "12345678901234567890",
                InvestmentSummary = "Investment in technology sector companies",
                CreatedBy = 123
            };

            var expectedId = Guid.NewGuid();
            ManagedAccountDetails capturedDetails = null;
            _mockService.Setup(x => x.CheckDuplicateNameAsync(command.ManagedAccountName, null))
                .ReturnsAsync(false);
            _mockService.Setup(x => x.CreateAsync(It.IsAny<ManagedAccountDetails>()))
                .Callback<ManagedAccountDetails>(details => capturedDetails = details)
                .ReturnsAsync(expectedId);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(capturedDetails);
            Assert.Equal(command.ManagedAccountName, capturedDetails.ManagedAccountName);
            Assert.Equal(command.Domicile, capturedDetails.Domicile);
            Assert.Equal(command.CommencementDate, capturedDetails.CommencementDate);
            Assert.Equal(command.InvestmentPeriodEndDate, capturedDetails.InvestmentPeriodEndDate);
            Assert.Equal(command.MaturityDate, capturedDetails.MaturityDate);
            Assert.Equal(command.CommitmentOutstanding, capturedDetails.CommitmentOutstanding);
            Assert.Equal(command.CommitmentOutstandingCurrency, capturedDetails.CommitmentOutstandingCurrency);
            Assert.Equal(command.BaseCurrency, capturedDetails.BaseCurrency);
            Assert.Equal(command.InvestmentManager, capturedDetails.InvestmentManager);
            Assert.Equal(command.Administrator, capturedDetails.Administrator);
            Assert.Equal(command.Custodian, capturedDetails.Custodian);
            Assert.Equal(command.LegalCounsel, capturedDetails.LegalCounsel);
            Assert.Equal(command.LEI, capturedDetails.LEI);
            Assert.Equal(command.InvestmentSummary, capturedDetails.InvestmentSummary);
            Assert.Equal(command.CreatedBy, capturedDetails.CreatedBy);
        }
    }
}

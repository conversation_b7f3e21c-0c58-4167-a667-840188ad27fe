﻿using System;
using System.Collections.Generic;
namespace Shared
{
    public static class Constants
    {
        public static readonly string[] AllowedDateFormats ={ "MM/d/yyyy", "MM/dd/yyyy","MMM/dd/yyyy","MMM/d/yyyy","M/d/yyyy","M/dd/yyyy","MM/dd/yy","MM/d/yy","M/d/yy","M/dd/yy","MMMM/dd/yyyy", "MMMM/d/yyyy", "MMMM/dd/yy","MMMM/d/yy","MMM/d/yy",
                    "MM-d-yyyy", "MM-dd-yyyy","MMM-dd-yyyy","MMM-d-yyyy","M-d-yyyy","M-dd-yyyy","MM-dd-yy","MM-d-yy","M-d-yy","M-dd-yy","MMMM-dd-yyyy", "MMMM-d-yyyy", "MMMM-dd-yy","MMMM-d-yy","MMM-d-yy",
                    "MM.d.yyyy", "MM.dd.yyyy","MMM.dd.yyyy","MMM.d.yyyy","M.d.yyyy","M.dd.yyyy","MM.dd.yy","MM.d.yy","M.d.yy","M.dd.yy","MMMM.dd.yyyy", "MMMM.d.yyyy", "MMMM.dd.yy","MMMM.d.yy","MMM.d.yy",
                    "yyyy-MM-dd'T'HH:mm:ss.fff'Z'"
            };
        public static readonly string[] AllowedTransactionTypes = { "Distribution",
            "Distributions (Net)",
            "Drawdowns",
            "Fair Market Value",
            "Follow-on Investment",
            "Initial Investment",
            "NAV",
            "Fees"
        };
        public static readonly string[] AllowedNegativeTransactionTypes = {
            "Drawdowns",
            "Follow-on Investment",
            "Initial Investment",
            "Fees"
        };
        public static readonly string[] AllowedCashflowStatus = {
            "Unrealized",
            "Realized"
        };
        public static readonly List<string> ChartLTM = ["Actual LTM", "Budget LTM", "Forecast LTM"];
        public static readonly List<string> ChartYTD = ["Actual YTD", "Budget YTD", "Forecast YTD"];
        public static readonly string CurrencyRateSource = "BulkUpload";
        public const string Investors = "Investors";
        public static readonly string InvestmentKPI = "Investment KPIs";
        public static readonly string IsWorkflow = "IsWorkflow";
        public static readonly string IsPendoEnabled = "IsPendoEnabled";
        public static readonly string AlphaString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        public const string FCFundCurrency = "FundCashflow Fund Currency";
        public const string FCReportingCurrency = "FundCashflow Reporting Currency";
        public const string FPFundCurrency = "FundPerformance Fund Currency";
        public const string FPReportingCurrency = "FundPerformance Reporting Currency";
        public const string Actual = "actual";
        public const string Budget = "budget";
        public const string IC = "ic";
        public const string IC2 = "ic2";
        public const string IC3 = "ic3";
        public const string IC4 = "ic4";
        public const string IC5 = "ic5";
        public const string SinceInception = "since inception";
        public const string ForeCast = "forecast";
        public const string ForecastYtd = "forecast ytd";
        public const string BudgetYtd = "budget ytd";
        public const string ActualYtd = "actual ytd";
        public const string ForecastLtm = "forecast ltm";
        public const string BudgetLtm = "budget ltm";
        public const string ActualLtm = "actual ltm";
        public const string MasterKPIs = "MasterKpis";
        public const string DealStaticHeader = "Deal Page- Deal Static Details";
        public const string DealStaticTrackRecordHeader = "Deal Page- Deal Track Record";
        public const string PCStaticHeader = "Portfolio Company Page- Static Details";
        public const string FundPcSheetName = "Portfolio Company Level";
        public const string FundLevelSheetName = "Fund Level";
        public const string ActualString = "Actual";
        public const string PortfolioCompany = "Portfolio Company";
        public const string Fund = "Fund";
        public const string StaticInformation = "Static Information";
        public const string StaticInformationTitle = "StaticInformation";
        public const string InvestmenstProfessional = "Investment Professionals";
        public const string KeyPerformanceIndicator = "Key Performance Indicator";
        public const string SustainableDevelopmentGoalsImages = "Sustainable Development Goals Images";
        public const string ValuationSummary = "Valuation Summary";
        public const string CompanyFinancials = "Company Financials";
        public const string Commentary = "Commentary";
        public const string BasicDetails = "Basic Details";
        public const string BasicDetailsTitle = "BasicDetails";
        public const string PortfolioCompanyFundHoldingDetails = "Portfolio Company Fund Holding Details";
        public const string PortfolioCompanyFundHoldingDetailsTitle = "PortfolioCompanyFundHoldingDetails";
        public const string FundStaticInformation = "Fund Details";
        public const string FundStaticInformationTitle = "FundStaticInformation";
        public const string FundTerms = "Fund Terms";
        public const string FundTermsTitle = "FundTerms";
        public const string TrackRecord = "Track Record";
        public const string FundIngestion = "Fund Ingestion";
        public const string TrackRecordTitle = "TrackRecord";
        public const string GeographicLocations = "Geographic Locations";
        public const string GeographicLocationsTitle = "GeographicLocations";
        public const string InvestmentProfessionals = "Investment Professionals";
        public const string InvestmentKpi = "InvestmentKPIs_";
        public const string OperationalKpi = "OperationalKPIs_";
        public const string ActualInvestment = "(Actual)";
        public const string DealCustomID = "DealCustomID";
        public const string DealId = "DealId";
        public const string FundName = "FundName";
        public const string FundId = "FundId";
        public const string CompanyName = "CompanyName";
        public const string CompanyAliasName = "Company Name";
        public const string Currency = "Currency";
        public const string PcInvestmentDate = "PCInvestmentDate";
        public const string InvestmentDate = "InvestmentDate";
        public const string EntryMultiple = "EntryMultiple";
        public const string EntryOwnershipPercent = "EntryOwnershipPercent";
        public const string CurrentExitOwnershipPercent = "CurrentExitOwnershipPercent";
        public const string EnterpriseValue = "EnterpriseValue";
        public const string EmployeeName = "EmployeeName";
        public const string LeadEmployeeName = "LeadEmployeeName";
        public const string BoardSeat = "BoardSeat";
        public const string ExitMethod = "ExitMethod";
        public const string InvestmentStage = "InvestmentStage";
        public const string SecurityType = "SecurityType";
        public const string DealSourcing = "DealSourcing";
        public const string DealsTitle = "Deals";
        public const string TransactionRole = "TransactionRole";
        public const string ValuationMethodology = "ValuationMethodology";
        public const string DealStatus = "Status";
        public const string CurrentEquityValueCalculated = "CurrentEquityValueCalculated";
        public const string Admin = "Admin";
        public const string ValuationDate = "ValuationDate";
        public const string InvestmentCost = "InvestmentCost";
        public const string RealizedValue = "RealizedValue";
        public const string UnrealizedValue = "UnrealizedValue";
        public const string TotalValue = "TotalValue";
        public const string Dpi = "Dpi";
        public const string Rvpi = "Rvpi";
        public const string GrossMultiple = "GrossMultiple";
        public const string GrossIRR = "GrossIRR";
        public const string Quarter = "Quarter";
        public const string Half = "Half";
        public const string Month = "Month";
        public const string CompanyLogo = "CompanyLogo";
        public const string FundFinancialsConfig = "Fund Financials";
        public const string LastQuarter = "Last Quarter";

        public const string Last3Month = "3M(Last 3 months)";
        public const string Last6Month = "6M(Last 6 months)";
        public const string Last1Year = "1 YR (Last 1 year)";
        public const string YearToDate = "YTD (Year to Date)";
        public const string DateRange = "Date Range";
        public const string Custom = "Custom";
        public const string FinancialsLast3Month = "3M (Last 3 months)";
        public const string FinancialsLast6Month = "6M (Last 6 months)";
        public const string Last3Year = "3 YR (Last 3 year)";


        public const string HeadquarterID = "HeadquarterID";
        public const string MasterCompanyName = "MasterCompanyName";
        public const string CompanyLegalName = "CompanyLegalName";
        public const string CompanyGroupId = "CompanyGroupId";
        public const string Sector = "Sector";
        public const string SubSector = "SubSector";
        public const string FinancialYearEnd = "FinancialYearEnd";
        public const string StockExchange_Ticker = "StockExchange_Ticker";
        public const string CompanyCurrency = "Currency";
        public const string Website = "Website";
        public const string CompanyStatus = "Status";
        public const string Region = "Region";
        public const string Country = "Country";
        public const string State = "State";
        public const string City = "City";
        public const string Designation = "Designation";
        public const string Email = "Email";
        public const string ContactNo = "ContactNo";
        public const string Education = "Education";
        public const string PastExperience = "PastExperience";
        public const string BusinessDescription = "BussinessDescription";
        public const string TRADING_RECORDS = "Trading Records";
        public const string SignificantEvents = "Significant Events";
        public const string AssessmentPlan = "Assessment Vs Initial Plan";
        public const string CommentaryPeriod = "Commentary Period";
        public const string CPeriod = "Period";
        public const string ExitPlan = "Exits Plan";
        public const string ImpactHighlights = "Impact Highlights";
        public const string FundSheetName = "Fund Level";
        public const string FundClosingDate = "FundClosingDate";
        public const string AccountType = "AccountType";
        public const string CarriedInterestPercent = "CarriedInterestPercent";
        public const string Clawback = "Clawback";
        public const string FirmName = "FirmName";
        public const string FundSize = "FundSize";
        public const string FundTerm = "FundTerm";
        public const string GPCatchupPercent = "GPCatchupPercent";
        public const string GPCommitment = "GPCommitment";
        public const string ManagementFee = "ManagementFee";
        public const string ManagementFeeOffset = "ManagementFeeOffset";
        public const string MaximumCommitment = "MaximumCommitment";
        public const string MaximumExtensionToFundTerm = "MaximumExtensionToFundTerm";
        public const string OrgExpenses = "OrgExpenses";
        public const string PreferredReturnPercent = "PreferredReturnPercent";
        public const string StrategyDescription = "StrategyDescription";
        public const string Strategy = "Strategy";
        public const string TargetCommitment = "TargetCommitment";
        public const string VintageYear = "VintageYear";
        public const string DataTypeDefault = "Default";
        public const string DataTypeFreeText = "Free Text";
        public const string DataTypeNumber = "Number";
        public const string DataTypeDate = "Date";
        public const string DataTypeList = "List";
        public const string DataTypeMultiple = "Multiple";
        public const string DataTypePercentage = "Percentage";
        public const string DataTypeCurrency = "Currency Value";
        public const string InvalidHeader = "Header";

        public const string TotalNumberOfInvestments = "TotalNumberOfInvestments";
        public const string RealizedInvestments = "RealizedInvestments";
        public const string UnRealizedInvestments = "UnRealizedInvestments";
        public const string TotalInvestedCost = "TotalInvestedCost";
        public const string TotalRealizedValue = "TotalRealizedValue";
        public const string TotalUnRealizedValue = "TotalUnRealizedValue";
        public const string NetMultiple = "NetMultiple";
        public const string NetIRR = "NetIRR";
        public const string Year = "Year";
        public const string CommitmentDate = "CommitmentDate";
        public const string InvestorId = "InvestorId";
        public const string InvestorsName = "Investor Name";
        public const string Commitment = "Commitment";
        public const string Ownership = "Ownership";
        public const string NetDrawn = "NetDrawn";
        public const string Recallable = "Recallable";
        public const string UndrawnCommitment = "UndrawnCommitment";
        public const string AstreaTransfer = "AstreaTransfer";
        public const string InvestorStake = "InvestorStake";
        public const string InvestorFundId = "FundId";
        public const string InvestorFirmID = "FirmID";
        public const string CommitmentAfterAstreaTransfer = "CommitmentAfterAstreaTransfer";
        public const string FundOperationalKpiSheetName = "Operational KPI";
        public const string OperationalKPIs = "OperationalKPIs";
        public const string CompanyKPIs = "CompanyKPIs";
        public const string ImpactKPIs = "ImpactKPIs";
        public const string ESG = "ESG";
        public const string CapTable = "Cap Table";
        public const string OtherCapTable = "Other CapTable";
        public const string Reports = "Reports";
        public const string MCapTable = "CapTable";
        public const string MOtherCapTable = "OtherCapTable";
        public const string CapTable1 = "CapTable1";
        public const string CapTable2 = "CapTable2";
        public const string CapTable3 = "CapTable3";
        public const string CapTable4 = "CapTable4";
        public const string CapTable5 = "CapTable5";
        public const string CapTable6 = "CapTable6";
        public const string CapTable7 = "CapTable7";
        public const string CapTable8 = "CapTable8";
        public const string CapTable9 = "CapTable9";
        public const string CapTable10 = "CapTable10";
        public const string OtherCapTable1 = "OtherCapTable1";
        public const string OtherCapTable2 = "OtherCapTable2";
        public const string OtherCapTable3 = "OtherCapTable3";
        public const string OtherCapTable4 = "OtherCapTable4";
        public const string OtherCapTable5 = "OtherCapTable5";
        public const string OtherCapTable6 = "OtherCapTable6";
        public const string OtherCapTable7 = "OtherCapTable7";
        public const string OtherCapTable8 = "OtherCapTable8";
        public const string OtherCapTable9 = "OtherCapTable9";
        public const string OtherCapTable10 = "OtherCapTable10";
        public const string FundKeyPerformanceIndicatorConfig = "Fund Key Performance Indicator";
        public const string FundKPIs = "FundKpis";
        public const string OtherKPIsConfig = "Other KPIs";
        public const string ModuleIdString = "ModuleId";
        public const int DefaultIdleTimeout = 240;// 4 mins
        public const int DefaultWarningTimeout = 60;// 1 min
        public const int DefaultIntervalTime = 300;//5 mins
        public const int FundFinancialsModule = 1001;
        public static readonly string[] FundColumns = {
            "Year",
            "Quarter",
            "Fund"
        };
        public static readonly string[] InvestorColumns = {
            "Year",
            "Quarter",
            "Fund Name",
            "Investor Name"
        };
        public static readonly string[] FundLevelColumns = {
            "Year",
            "Quarter"
        };
        public static readonly string[] PCColumns = {
            "Company",
            "Region",
            "Country",
            "Headquarters",
            "Sector",
            "Sub Sector",
            "IsPublicCompany",
            "Bloomberg Ticker",
            "Business Description"
        };
        public static readonly string[] DealStaticColumns = {
            "Investment Date",
            "Exit Type",
            "Ownership",
            "Valuation Currency",
            "Entry Ownership"
        };
        public static readonly string[] DealTrackRecordColumns = {
            "Invested Capital",
            "Realized Value",
            "Unrealized Value",
            "Total Value",
            "DPI",
            "RVPI",
            "TVPI",
            "Gross IRR",
            "Deal Status"
        };
        public static readonly string[] MasterKPIsExcelColHeaders = {
            "KPI",
            "Id",
            "Header",
            "Quarter",
            "Month",
            "Year",
            "Half",
            "IsYTD",
            "KPIValue",
            "CreatedBy",
            "CreatedOn",
            "IsNumeric",
            "ModuleID"
        };

        public static readonly string[] DealStaticDataConfiguration = {
           "Deal                 ",
            "Fund Name            ",
            "Portfolio Company    ",
            "Currency             ",
            "Investment Date      ",
            "Entry Multiple       ",
            "Entry Ownership      ",
            "Current/Exit Ownershi",
            "Enterprise Value     ",
            "Sourcing Professional",
            "Lead Professional    ",
            "Board Seat           ",
            "Exit Method          ",
            "Investment Stage     ",
            "Security Type        ",
            "Deal Sourcing        ",
            "Transaction Role     ",
            "Valuation Methodology"
        };

        public static readonly string[] DealTrackRecordDataConfiguration = {
            "Invested Capital",
            "Realized Value",
            "Unrealized Value",
            "Total Value",
            "DPI",
            "RVPI",
            "TVPI",
            "Gross IRR",
            "Deal Status"
        };

        private static readonly Dictionary<string, string> _LPConfigurationEnumFields = new()
        {
            { "CI - Company Information", StaticInformation },
            { "CI - Company Logo & Name", CompanyName },
            { "CI - Investment Professionals", InvestmenstProfessional },
            { "CI - Geographical Location", GeographicLocations },
            { "CI - Business Description", BusinessDescription }
        };

        public static Dictionary<string, string> LPConfigurationEnumFields
        {
            get { return _LPConfigurationEnumFields; }
        }

        public const string PeriodTypeYear = "year";
        public const string PeriodTypeQuarter = "quarter";
        public const string PeriodTypeMonth = "month";

        public const string InvalidFormat = "Invalid Format, Please enter the value with correct format";

        public static readonly string[] ExcelHeadersList =
        {
            "(actual)",
            "(budget)" ,
            "(forecast)",
            "(ic)"
        };
        public const string ValuationDataPrefix = "VD-";
        public const string PCStaticInformation = "CI-";
        public const string DealStaticInformationPrefix = "DS-";
        public const string DealTrackRecordPrefix = "DT-";
        public const string FundStaticPrefix = "FS-";
        public const string FundTrackRecordPrefix = "FT-";
        public const string TradingKPIPrefix = "Tr-";
        public const string InvestmentKPIPrefix = "Inv-";
        public const string OperationalKPIPrefix = "Op-";
        public const string InvestorInformationPrefix = "InvInfo-";
        public const string FundStaticInformationPrefix = "Fd-";
        public const string InvestorFundsPrefix = "InvFds";
        public const string TradingKPICurrency = "Financial Currency";
        public const string InvestmentKPICurrency = "Investment Currency";
        public const string InvestorGeographicalLocations = "Geographical Locations";
        public const string InvestorInformation = "Investor Information";
        public const string InvestorFunds = "Invested Funds";
        public const string InvestorDashboard = "DashBoard";
        public const string InvestorBusinessDescription = "Business Description";
        public const string InvestorName = "InvestorName";
        public const string InvestorTypeId = "InvestorTypeId";
        public const string InvestorWebsite = "Website";
        public const string TotalCommitment = "TotalCommitment";
        public const string Customfield = "Customfield";
        public const string InvestorDescription = "BusinessDescription";
        public const string CompanyPerformance = "Company Performance";
        public const string ValuationData = "Valuation Data";
        public const string MainDashboard = "Dashboard";
        public const string CashFlowTransactionType = "Transaction Type";
        public const string CashFlowPort = "Port";
        public const string ValuationDataModuleName = "ValuationData";
        public const string Adhoc = "Adhoc";
        public const string Investor_Investment_Summary = "Investor_Investment_Summary";
        public const string InvestorFundSheetName = "Investor Investment Summary";
        public const string ExcludeEndPoint = "/api/report/get/adhoc";
        public const string ExcludeEndPointPod = "/services/api/report/get/adhoc";
        public const string InvestorInvestedCapital = "InvestedCapital";
        public const string InvestorTotalValue = "TotalValue";
        public const string InvestorRealizedValue = "RealizedValue";
        public const string InvestorUnRealizedValue = "UnRealizedValue";
        public const string InvestorTotalFunds = "TotalFunds";
        public const string InvestorTotalPortfolioCompanys = "PortfolioCompanies";
        public const string InvestorDPI = "DPI";
        public const string InvestorRVPI = "RVPI";
        public const string InvestorTrackRecord = "InvestorTrackRecord";

        #region sheetNames
        public const string ProfitAndLossSheet = "profit & loss";
        public const string BalanceSheet = "balance sheet";
        public const string CashFlowSheet = "cashflow";
        public const string CompanyKpiSheet = "company kpi";
        public const string InvestmentKpiSheet = "investment kpi";
        public const string OperationalKpiSheet = "operational kpi";
        public const string CashFlowUploadSheet = "CashFlow";
        #endregion

        public const string ValuationCapitalCall = "CapitalCall";
        public const string ValuationCapitalDistributed = "CapitalDistribution";
        public const string ValuationFeesAndExpenses = "FeesAndExpenses";
        public const string ValuationUnrealizedGainOrLoss = "UnrealizedGainOrLoss";
        public const string ValuationClosingNAV = "ClosingNAV";
        public const string ValuationFundName = "Fund Name";
        public const string Thousands = "thousands";
        public const string Millions = "millions";
        public const string Billions = "billions";
        public const string Absolute = "absolute";
        public const string KpiType = "@KpiType";
        public const string KpiId = "@KpiId";
        public const string UserDetailId = "@UserId";
        public const string ModuleId = "@ModuleId";
        public const string Id = "@Id";
        public const string TopHoldingPortfolioCompany = "Portfolio Company";
        public const string TopHoldingFund = "Fund";
        public const string TopHoldingStatus = "Status";
        public const string TopHoldingPrefix = "**";
        public const string TopHoldingInvestmentCostFileName = "By Investment Cost";
        public const string TopHoldingUnrealisedValueFileName = "By Unrealised Value";
        public const string TopHoldingTotalValueFileName = "By Total Value";
        public const string TopHoldingGrossMultipleFileName = "By GrossMultiple";
        public const string TopHoldingInvestmentCost = "TopHoldingInvestmentCost";
        public const string TopHoldingUnrealisedValue = "TopHoldingUnrealisedValue";
        public const string TopHoldingTotalValue = "TopHoldingTotalValue";
        public const string TopHoldingGrossMultiple = "TopHoldingGrossMultiple";
        public const string TopHoldingInvestorInvestmentCost = "TopHoldingInvestorInvestmentCost";
        public const string TopHoldingInvestorUnrealisedValue = "TopHoldingInvestorUnrealisedValue";
        public const string TopHoldingInvestorTotalValue = "TopHoldingInvestorTotalValue";
        public const string TopHoldingInvestorGrossMultiple = "TopHoldingInvestorGrossMultiple";
        public const string NotAvailable = "NA";
        public const string AuditLogDateFormat = "dd-MM-yyyy HH:mm";
        public const string AuditLogString = "AuditLog";
        public const string AuditLogNumberFormat = "{0:#,0.0}";
        public const string TopHoldingByCompanyValuation = "Company Valuation";
        public const string KPIInvestmentTable = "KPI - Investment Table";
        public const string KPIOperationalTable = "KPI - Operational Table";
        public const string KPICompanyTable = "KPI - Company Table";
        public const string KPIImpactTable = "KPI - Impact Table";
        public const string KPITradingTable = "KPI - Trading Record Table";
        public const string KPICreditTable = "KPI - Credit Table";

        public const string InvestmentFootNoteTag = "<Investment/>";
        public const string OperationalFootNoteTag = "<Operational/>";
        public const string CompanyFootNoteTag = "<Company/>";
        public const string ImpactFootNoteTag = "<Impact/>";
        public const string TradingFootNoteTag = "<Trading/>";
        public const string CreditFootNoteTag = "<Credit/>";

        public const string InvestmentEmptyFootNoteTag = "[investmentFootnote]";
        public const string OperationalEmptyFootNoteTag = "[footnoteOperational]";
        public const string CompanyEmptyFootNoteTag = "[footnoteCompany]";
        public const string ImpactEmptyFootNoteTag = "[footnoteImpact]";
        public const string TradingEmptyFootNoteTag = "[tradingFootnote]";
        public const string CreditEmptyFootNoteTag = "[footnoteCredit]";
        public const string CompanyValuation = "TopHoldingByCompanyValuation";
        public const string AdhocMonthly = "Monthly";
        public const string AdhocQuarterly = "Quarterly";
        public const string AdhocYearly = "Yearly";
        public const string OperationalHeaderKPI = "KpiId";
        public const string OperationalHeaderKPIInfo = "KPI Info";
        public const string NewTemplate = "New Template";
        public const string InternalReportExcel = "InternalReport_Import.xlsx";
        public const string ValuationReportExcel = "ValuationReport_Import.xlsx";
        public const string MonthlyReportExcel = "Monthly Report.xlsx";
        public const string GrowthReportExcel = "GrowthReport.xlsx";
        public const string InternalReportMonthlyExcel = "InternalReportMonthlyExcel_Import.xlsx";
        public const string InternalReportSheet = "InternalReport";
        public const string ValuationReportSheet = "ValuationReport";
        public const string MonthlyReportSheet = "Monthly Report";
        public const string MonthlyReportName = "MonthlyReport";
        public const string Quarter1 = "Q1";
        public const string Quarter2 = "Q2";
        public const string Quarter3 = "Q3";
        public const string Quarter4 = "Q4";
        public const string SQuarter1 = "q1";
        public const string SQuarter2 = "q2";
        public const string SQuarter3 = "q3";
        public const string SQuarter4 = "q4";
        public const string FinancialYearFy = "FY";
        public const string TradingRecordsDraft = "TradingRecordsDraft_Import.xlsx";
        public const string InvestmentRecordsDraft = "InvestmentRecordsDraft_Import.xlsx";
        public const string FinancialYearEndFy = "FYE-";
        public const string DefaultFinancialYearEndFy = "Dec";
        public const string FinancialVariance = "%Variance";
        public const string NumberVariance = "#Variance";
        public const string KpiInfoPercent = "%";
        public const string KpiInfoMultiple = "x";
        public const string KpiInfoCurrency = "$";
        public const string KpiInfoNumber = "#";
        public const string KpiInfoText = "Text";
        public const string HeaderColorCode = "#021155";
        public const string PeriodMonthColorCode = "#722167";
        public const string PeriodColorCode = "#DFE1F2";
        public const string CompanyColorCode = "#EFF0F9";
        public const string ValueTypeColorCode = "#F7F8FC";
        public const string Backgroundcolor = "#808080";
        public const string ValueTypeBorderColor = "#BFBFBF";
        public const string FormulaBgColor = "#D2EDFD";
        public const string KpiTitle = "KPI";
        public const string Measure = "Measure";
        public const string LineItemTitle = "LineItem";
        public const string InstrumentTitle = "Instrument";
        public const string OriginalTransaction = "Original Transaction";
        public const string ValuationStartPeriod = "ValuationPeriod";
        public const string ConsolidatedPortfolioCompanyId = "PCId";
        public const string ConsolidatedFundId = "FDId";
        public const string ConsolidatedDealId = "DId";
        public const string ConsolidatedReportExcel = "Consolidated_Import.xlsx";
        public const string PCOperationalDownloadExcel = "OperationalKPI_Download.xlsx";
        public const string PCInvestmentDownloadExcel = "InvestmentKPI_Import.xlsx";
        public const string FinancialDownloadExcel = "Financials_Download.xlsx";
        public const string PCOperationalDownloadDraftExcel = "OperationalKPI_Draft.xlsx";
        public const string CompanyKpi_ImportExcel = "CompanyKPI_Import.xlsx";
        public const string ConsolidatedReportSheet = "FundOfFund";
        public const string ConstantsNoKey = "No key!";
        public const string ActualVsBudget = "Change Actual vs Budget";
        public const string ActualVsForecast = "Change Actual vs Forecast";
        public const string BudgetString = "Budget";
        public const string ForecastString = "Forecast";
        public const string ICString = "IC";
        public const string IC2String = "IC2";
        public const string IC3String = "IC3";
        public const string IC4String = "IC4";
        public const string IC5String = "IC5";
        public const string CalculationQuarterly = "%QoQ";
        public const string CalculationMonthly = "%MoM";
        public const string CalculationAnnually = "%YoY";
        public const string CalculationMTD = "MTD";
        public const string CalculationYTD = "YTD";
        public const string CalculationFY = "FY";
        public const string CalculationLTM = "LTM";
        public const string BudYTD = "Bud.YTD";
        public const string BudVar = "Bud.Var";
        public const string PriorYtd = "Prior YTD";
        public const string PriorVar = "Prior Var";
        public const string CalculationNTM = "NTM";
        public const string ConsolidatedReportHeaderColorCode = "#021155";
        public const string NetDifferenceInvestment = "Net difference (Investment)";
        public const string NetDifferenceLTM = "Net Difference (LTM)";
        public const string YTD = "YTD";
        public const string LTM = "LTM";
        public const string Color = "color";
        public static readonly string[] CalculationValuationList =
        {
            "Net difference (Investment)",
            "Net Difference (LTM)",
            "YTD"
        };
        public static readonly string[] MonthlyReportStaticColumns =
        {
            "Bud.Var",
            "Bud.YTD",
            "YTD",
            "Prior Var",
            "Prior YTD",
            "Color"
        };

        public const int CompanyColumn = 3;
        public const int ActualVsBudgetRowId = 100;
        public const int ActualVsForecastRowId = 101;
        public const string ConsolidatedDownloadGuid = "Guid";
        public const string GroupColorCode = "#F9EBFF";
        public const string GroupColorBlackCode = "#000000";

        public const string FormulaQuarterly = "Quarterly";
        public const string FormulaMonthly = "Monthly";
        public const string FormulaYearly = "Yearly";
        public const string FormulaPL = "PL";
        public const string FormulaBS = "BS";
        public const string FormulaCF = "CF";
        public const string FormulaTradingRecords = "TradingRecords";
        public const string FormulaCreditKPI = "CreditKPI";
        public const string FormulaOperational = "Operational";
        public const string FormulaInvestment = "Investment";
        public const string FormulaCompany = "Company";
        public const string FormulaCapTable = "CapTable";
        public const string FormulaImpact = "Impact";
        public const string OPERATIONAL_KPI_PREFIX = "Opr";
        public const string INVESTMENT_KPI_PREFIX = "Inv";
        public const string COMPANY_KPI_PREFIX = "Com";
        public const string Cap_KPI_PREFIX = "Cap";
        public const string IMPACT_KPI_PREFIX = "Imp";
        public const string PROFIT_LOSS_KPI_PREFIX = "Pro";
        public const string BALANCE_SHEET_KPI_PREFIX = "Bal";
        public const string CASHFLOW_KPI_PREFIX = "Cas";
        public const string CREDIT_KPI_PREFIX = "Cre";
        public const string TRADING_RECORDS_PREFIX = "Tra";
        public const string TradingRecords_KPI = "TradingRecords";
        public const string Investment_KPI = "Investment KPIs";
        public const string Operational_KPI = "Operational KPIs";
        public const string Company_KPI = "Company KPIs";
        public const string InvestmentKPIs = "InvestmentKPIs";
        public const string OperationalKPIss = "OperationalKPIs";
        public const string OperationalKPIDownloadName = "Operation KPI";
        public const string Impact_KPI = "Impact KPIs";
        public const string Credit_KPI = "Credit KPIs";
        public const string CreditKPI = "CreditKPI";


        public const string ConsolidatedTemplateUniqueName = "Consolidated Template";
        public const string ConsolidatedNewInvestmentsUniqueName = "New Investments";
        public const string ConsolidatedFrom = "from";
        public const string ConsolidatedTo = "to";
        public const string KpiFinancials = "Financials";
        public const string FundFinancialsStr = "FundFinancials";
        public const string OtherKPIs = "OtherKPIs";
        public const string InvestmentKpiTitle = "Investment KPIs";
        public const string MonthlyType = "M";
        public const string QuarterlyType = "Q";
        public const string AnnuallyType = "A";
        public const string YearlyType = "Y";
        public const string HalfAnnualType = "H";
        public const string PY = "PY";
        public static readonly string[] InvestorBulkUploadColumns = {
            "InvestorName",
            "FundName",
        };
        public const string Ok = "Ok";
        public const string InternalServerError = "InternalServerError";
        public const string PeriodTypeAnnually = "Annually";
        public const string CommonTemplate = "KpiTemplate";
        public const string MonthlyReportingTitle = "Portfolio Company Monthly Reporting";
        public const string CapTableTemplate = "Cap_Table_Export";
        public const string UserListTemplate = "UsersList_Download.xlsx";
        public const string UserListSheet = "Users";
        public const string WhiteColor = "#ffffff";
        public static readonly string DarkBlue = "#021155";
        public static readonly string BeatColor = "#021155";
        public static readonly string Black = "#000000";
        // Cell format list
        public static readonly string CurrencyFormat = "#,##0.0 ;[Red](#,##0.0);";
        public static readonly string MonthlyReportFormat1 = "#,##0.0;[Red](#,##0.0)";
        public static readonly string MonthlyReportFormat2 = "#,##0;[Red](#,##0)";
        public static readonly string MonthlyReportMultipleFormat1 = "#,##0.0x;-#,##0.0x;0x";
        public static readonly string MonthlyReportMultipleFormat2 = "#,##0x;-#,##0x;0x";
        public static readonly string MonthlyReportPercentageFormat1 = "#,##0.0%;-#,##0.0%";
        public static readonly string MonthlyReportPercentageFormat2 = "#,##0%;-#,##0%";
        public static readonly string MonthlyReportVarianceFormat = "#,##0%;-#,##0%";
        public static readonly string CurrencyFormatWithTwoDecimal = "#,##0.00 ;[Red](#,##0.00)";
        public static readonly string CurrencyFormatWithZeroDecimal = "#,##0 ;[Red](#,##0)";
        public static readonly string CurrencyFormatWithThreeDecimal = "#,##0.000 ;[Red](#,##0.000)";
        public static readonly string CurrencyFormatWithFourDecimal = "#,##0.0000 ;[Red](#,##0.0000)";
        public static readonly string CurrencyFormatWithFiveDecimal = "#,##0.00000 ;[Red](#,##0.00000)";
        public static readonly string CurrencyFormatWithSixDecimal = "#,##0.000000 ;[Red](#,##0.000000)";
        public static readonly string NegativeNumberFormat = "#,##0;[Red](#,##0)";
        public static readonly string NumberFormatSingleDecimal = "#0.0";
        public static readonly string NumberFormatDoubleDecimal = "#0.00";
        public static readonly string NumberFormatTripleDecimal = "#0.000";
        public static readonly string NumberFormatFourDecimal = "0.0000";
        public static readonly string NumberFormatFiveDecimal = "#0.00000";
        public static readonly string NumberFormatSixDecimal = "#0.000000";
        public static readonly string positiveFormat = "#,##0.0";
        public static readonly string percentFormat = "#0.0%";
        public static readonly string MultipleFormat = "#0.0x";
        public static readonly string MultipleFormatWithTwoDecimal = "#0.00x";
        public static readonly string MultipleFormatWithThreeDecimal = "#0.000x";
        public static readonly string MultipleFormatWithFourDecimal = "#0.0000x";
        public static readonly string MultipleFormatWithFiveDecimal = "#0.00000x";
        public static readonly string MultipleFormatWithSixDecimal = "#0.000000x";
        public static readonly string NegativePercentFormat = "#0.0%;[Red](0.0%)";
        public static readonly string NegativePercentFormatWithTwoDecimal = "#0.00%;[Red](0.00%)";
        public static readonly string NegativePercentFormatWithThreeDecimal = "#0.000%;[Red](0.000%)";
        public static readonly string NegativePercentFormatWithFourDecimal = "#0.0000%;[Red](0.0000%)";
        public static readonly string NegativePercentFormatWithFiveDecimal = "#0.00000%;[Red](0.00000%)";
        public static readonly string NegativePercentFormatWithSixDecimal = "#0.000000%;[Red](0.000000%)";
        public static readonly string NumberFormat = "#,##";
        public const int CellValueFontSize = 11;
        public const string Half_Annual = "Half-Annual";
        public const int CellHeaderFontSize = 12;
        public const int CellDefaultWidth = 25;
        public const double MonthlyReportColWidth = 8.43;
        public const double MonthlyReportFundRowHeight = 15.75;
        public const double MonthlyReportTitleRowHeight = 18.75;
        public const int MonthlyReportRowHeight = 15;
        public const int KpiPeriodstartingRow = 6;
        public const int MonthlyReportHeaderColStart = 1;
        public const int MonthlyReportHeaderRowStart = 4;
        public const int MonthlyReportFundRow = 3;
        public const int DividebyPercentageKpi = 100;
        public const string CellFormat = "-1-";
        public const int EsgKpi = 10;
        public const string CashflowCompanyName = "Company Name";
        public const string CashflowCapitalInvested = "Capital Invested";
        public const string CashflowRealized = "Realized Value";
        public const string CashflowUnrealized = "Unrealized Value";
        public const string CashflowTotal = "Total Value";
        public const string CashflowColor = "Color";
        public const string CashflowGrossIRR = "Gross IRR";
        public const string CashflowGrossTVPI = "Gross TVPI";
        public const string CashflowTransactionType = "Transaction Type";
        public const string CashflowTransactionDate = "Transaction Date";
        public const string CashflowTransactionValue = "Transaction Value";
        public const string CashflowDate = "Date";
        public const string Cashflowdate = "date";
        public const string CashflowName = "name";
        public const string CashflowValue = "value";
        public const string Cashflowcurrency = "currency";
        public const string CashflowReportingtransactionType = "transactionType";
        public const string CashFlowReportingCurrency = "Reporting Currency";
        public const string CashflowLowerCapitalInvested = "capitalInvested";
        public const string CashflowLowerRealizedValue = "realizedValue";
        public const string CashflowLowerUnrealizedValue = "unrealizedValue";
        public const string CashflowLowerTotalValue = "totalValue";
        public const string CashflowIRR = "IRR";
        public const string CashflowTVPI = "TVPI";
        public const string CashflowisRealizedValue = "isRealizedValue";
        public const string CashflowisExpense = "isExpense";
        public const string CashflowisTotal = "isTotal";
        public const string CashflowTotalRealized = "Total Realized";
        public const string CashflowRealizedHex = "#DCFAE4";
        public const string CashflowUnRealizedHex = "#FFE8BF";
        public const string RepositorySection = "RepositorySection";
        public const string Authorization = "Authorization";
        public const string UserDetails = "User";
        public const string DashboardId = "DashboardId";
        public const string S3CashFlowFolder = "Cashflow";
        public const string S3ExcelPluginFolder = "ExcelPlugIn";
        public const string ExcelPlugInEmpty = "Empty file uploaded.";
        public const string ExcelPlugInInvalidFile = "Invalid file type uploaded.";
        public const string ExcelPlugInSuccessful = "Plugin upload successful";
        public const string ExcelPlugInFailed = "Plugin upload failed";
        public const string S3ExcelPluginGetFileNameError = "Failed to fetch the latest uploaded file name. Please check if the file exists and try again.";
        public const string  FundFinancials = "FundFinancials";
        public const string FundKpis = "FundKpis";
        public static readonly string[] KpiTypes = {
            "InvestmentKPIs",
            "OperationalKPIs",
            "CompanyKPIs",
            "ImpactKPIs",
            "CreditKPI",
            "TradingRecords",
            "ProfitLoss",
            "BalanceSheet",
            "CashFlow",
            "CustomTable1",
            "CustomTable2",
            "CustomTable3",
            "CustomTable4",
            "OtherKPI1",
            "OtherKPI2",
            "OtherKPI3",
            "OtherKPI4",
            "OtherKPI5",
            "OtherKPI6",
            "OtherKPI7",
            "OtherKPI8",
            "OtherKPI9",
            "OtherKPI10",
        };
        public const string Monthly = "Monthly";
        public const string Quarterly = "Quarterly";
        public const string Annually = "Annually";
        public const string Annual = "Annual";
        public const string HalfAnnual = "Half-Annual";
        public const string S3FileUploadMessage = "File upload failed with S3 file uploadService.";
        public const string PortfolioCompanyId = "CompanyId";
        public const string CurrencyRatesApiSource = "SystemApi";
        public const string CurrencyRatesBulkUploadSource = "BulkUpload";

        public const string OriginalInvestedCapital = "OriginalInvestedCapital";
        public const string OriginalRealizedValue = "OriginalRealizedValue";
        public const string OriginalUnrealizedValue = "OriginalUnrealizedValue";
        public const string OriginalTotalValue = "OriginalTotalValue";
        public const string InvestorFundDetails = "Investor Fund Details";
        public const string FundDetailsTitle = "FundDetails";
        public const string InvestorCompanyDetails = "Investor Company Details";
        public static readonly string[] ImportExcelHeaders = {
            "Id",
            "Header",
            "Quarter",
            "Month",
            "Year",
            "KPIValue",
            "CreatedBy",
            "CreatedOn"
        };
        public const string InvalidFormula = "External link found. Please check.";
        public const string KpiFormatError = "Please enter value in correct format";
        public const string Period = "Period";
        public const string PeriodDate = "PeriodDate";
        public const string DataAnalyticsFiles = "DataAnalyticsFiles";
        public const string Upload = "Upload";
        public const string FileUploadSuccess = "File uploaded successfully.";
        public const string FileDeletedSuccess = "File deleted successfully.";
        public const string FileFailedSuccess = "File deleted failed.";
        public const string FileNotFound = "File not found.";
        public const string error = "error";
        public const string FOFFund = "Fund";
        public static readonly string FOFHeader2Color = "#692764";
        public const string FOFOP = FormulaOperational;
        public const string DataSheet = "Data";
        public const string ZipExtn = "application/zip";
        public const int FundTradingRecords = 100;
        public const int FundInvestments = 101;
        public static readonly string[] NumberStaticFields = {
            "Dpi","EnterpriseValue","EntryMultiple","EntryOwnershipPercent","GrossIRR","GrossMultiple","InvestmentCost","RealizedValue","Rvpi","TotalValue","UnrealizedValue",
            "Year","VintageYear","UnRealizedInvestments","TotalValue","TotalUnRealizedValue","TotalRealizedValue","TotalNumberOfInvestments","TotalInvestedCost","CurrentExitOwnershipPercent",
            "TargetCommitment","RealizedInvestments","PreferredReturnPercent","NetMultiple","NetIRR","MaximumExtensionToFundTerm","MaximumCommitment","GPCommitment","GPCatchupPercent","FundSize",
        };
        public static readonly string[] DateStaticFields = {
            "FundClosingDate",
            "InvestmentDate",
            "ValuationDate",
        };
        public static readonly string[] RestrictedHeaderMethods = { "PATCH", "COPY", "HEAD", "LINK", "UNLINK", "PURGE", "LOCK", "UNLOCK", "PROPFIND", "VIEW", "TRACE", "BADMETHOD" };
        public const string Access_Method_Error = "Method is not allowed.";
        public const string Request_X_Method_Override = "x-method-override";
        public const string Request_Get = "GET";
        public const string Access_Header_Error = "Request Header is not allowed";
        public static readonly string[] Request_Header_Restricted = { "X-HTTP-Method-Override", "X-Method-Override" };
        public const string SignalRConnectionKey = "SignalR-Connection-Key-{0}";
        public const string ESGLineItem = "ESGLineItem";
        public const string FundTrStaging = "FundTrackRecord_staging_";
        public const string FundStaging = "Funds_staging_";
        public const string PcStaging = "Pc_staging_";
        public const string DealStaging = "Deal_staging_";
        public const string DealTrStaging = "DealTrackRecord_staging_";
        public const string FundTrStagingCustom = "FundTrackRecord_staging_custom_";
        public const string FundStagingCustom = "Funds_staging_custom_";
        public const string FundInvestmentKpiStaging = "Funds_investmentkpis_staging_";
        public const string FundOperationalKpiStaging = "Funds_operationalkpis_staging_";
        public const string FundTradingRecordStaging = "Funds_tradingrecords_staging_";
        public const string FundFinancialsStaging = "Funds_kpis_staging_";
        public const int QuarterColumnNumber = 2;
        public const int YearColumnNumber = 1;
        public const int CompanyColumnNumber = 4;
        public const string ActualHex = "#26A69A";
        public const string BudgetHex = "#F39C12";
        public const string ForecastHex = "#69B02A";
        public const string ICHex = "#0045D9";
        public const string IC2Hex = "#1A6AFF";
        public const string IC3Hex = "#4089FF";
        public const string IC4Hex = "#8CC0FF";
        public const string IC5Hex = "#B3D7FF";
        public const string AuditLog = "AuditLog";
        public const string FileUpload = "File Upload";
        public const string AsEstimateMonth = "Dec ";
        public const string EstimatePrefix = "E";
        public const string Active = "Active";
        public const string InActive = "InActive";
        public const int RowStartIndex = 2;
        public const string UploadedFiles = "Uploaded Files";
        public const string uploaded = "Uploaded";
        public const string Final = "Final";
        public const string FinalFiles = "Final Files";
        public const string RecycleBin = "Recycle Bin";
        public const string PeriodType = "PeriodType";
        public const string AnalyticKpiType = "KpiType";
        public const string BulkUploadCreditKpi = "Credit KPI";

        public const string BulkUploadTradingKpi = "Trading Records";
        public const string PcId = "PortfolioCompanyId";
        public const string DocumentInfoRootDirectory = "DocumentInformation";
        public const string ZipContentType = "application/zip";
        public const string OctetStreamContentType = "application/octet-stream";
        public const string ContentDispositionContentType = "Content-Disposition";
        public const string BulkUploadProfitLoss = "Profit & Loss";
        public const string BulkUploadBalanceSheet = "Balance Sheet";
        public const string BulkUploadCashFlow = "CashFlow";
        public const string BulkUploadOperationalKpi = "Operational KPI";
        public const string BulkUploadInvestmentKpi = "Investment KPI";
        public const string BulkUploadTradingRecords = "Trading Records";
        public const string BulkUploadCompanyKpi = "Company KPI";
        public const string BulkUploadImpactKpi = "Impact KPI";
        public const string InvestmentKpiStaging = "investment_kpi_staging_";
        public const string FundFinancialsKpiStaging = "fund_financials_kpi_staging_";
        public const string TradingKpiStaging = "trading_kpi_staging_";
        public const string ExcelKpiId = "Id";
        public static readonly string[] MonthlyReportColors = {
            "red",
            "green",
            "orange",
        };
        public const string FirstColumn = "A1";
        public const string SecondColumn = "B1";
        public const int CellMaxLength = 15;
        public const int CellValueMaxLength = 24;
        public const int HeaderLength = 2;
        public const string ImpactKpiStaging = "impact_kpi_staging_";
        public const string SheetReference = "Period Reference";
        public const string Manual = "Manual";
        public const string CapTablePeriodStaging = "cap_period_staging_";
        public const string CapTableDataStaging = "cap_table_kpi_staging_";
        public const string HighlightColorCode = "#FFECB3";
        public const string ProfitAndLossStaging = "profitandloss_staging_";
        public const string BalanceSheetStaging = "balancesheet_staging_";
        public const string CashFlowStaging = "cashflow_staging_";
        public const string AccessControl = "Access-Control-Expose-Headers";
        public const string ContentDisposition = "Content-Disposition";


        public const string DisplayOrder = "DisplayOrder";
        public const string MappingId = "MappingId";
        public const string ValueTypeId = "ValueTypeId";
        public const string HeaderValue = "HeaderValue";
        public const string KpiTypeId = "KpiTypeId";
        public const string ColumnNumber = "ColumnNumber";
        public const string CapValueId = "CapValueId";
        public const string FootNote = "FootNote";
        public const string CreditKpiStaging = "credit_kpi_staging_";
        public const string CustomKpiStaging = "custom_kpi_staging_";
        public const string OtherKpiStaging = "other_kpi_staging_";
        public const string CompanyKpiStaging = "company_kpi_staging_";
        public const string OperationalKpiStaging = "operational_kpi_staging_";
        public const string MonthlyReportStaging = "monthly_report_staging_";
        public const int DiscardGettingEdited = 31;
        public const string WorkflowDiscard = "Discarded";
        public const string WorkflowPublished = "Published";
        public const string WorkFlowSubject = "Workflow";
        public const string Rework = "Rework";
        public const string Stage = "Stage";
        public static readonly string ProfitLossConfigName = "ProfitLoss";
        public static readonly string BalanceSheetConfigName = "BalanceSheet";
        public static readonly string CashflowConfigName = "CashFlow";
        public const string CurrencyRatesSystemApiSource = "SystemAPI";
        public const string FeatureMappingStaging = "featureMapping_Staging_";
        public const string MonthlyReport = "monthly report";
        public static readonly string[] CommentaryStaticFields = { "Significant Events", "Assessment Vs Initial Plan", "Exits Plan", "Impact Highlights" };
        public const string Custom_Table1 = "Custom Table1";
        public const string Custom_Table2 = "Custom Table2";
        public const string Custom_Table3 = "Custom Table3";
        public const string Custom_Table4 = "Custom Table4";
        public const string Custom_Table5 = "Custom Table5";
        public const string Custom_Table6 = "Custom Table6";
        public const string Custom_Table7 = "Custom Table7";
        public const string Custom_Table8 = "Custom Table8";
        public const string Custom_Table9 = "Custom Table9";
        public const string Other_KPI1 = "Other KPI1";
        public const string Other_KPI2 = "Other KPI2";
        public const string Other_KPI3 = "Other KPI3";
        public const string Other_KPI4 = "Other KPI4";
        public const string Other_KPI5 = "Other KPI5";
        public const string Other_KPI6 = "Other KPI6";
        public const string Other_KPI7 = "Other KPI7";
        public const string Other_KPI8 = "Other KPI8";
        public const string Other_KPI9 = "Other KPI9";
        public const string Other_KPI10 = "Other KPI10";
        public const string Other_KPI11 = "Other KPI11";
        public const string Other_KPI12 = "Other KPI12";
        public const string Other_KPI13 = "Other KPI13";
        public const string Other_KPI14 = "Other KPI14";
        public const string Other_KPI15 = "Other KPI15";
        public const string CustomTable1 = "CustomTable1";
        public const string CustomTable2 = "CustomTable2";
        public const string CustomTable3 = "CustomTable3";
        public const string CustomTable4 = "CustomTable4";
        public const string CustomTable5 = "CustomTable5";
        public const string CustomTable6 = "CustomTable6";
        public const string CustomTable7 = "CustomTable7";
        public const string CustomTable8 = "CustomTable8";
        public const string CustomTable9 = "CustomTable9";
        public const string OtherKPI1 = "OtherKPI1";
        public const string OtherKPI2 = "OtherKPI2";
        public const string OtherKPI3 = "OtherKPI3";
        public const string OtherKPI4 = "OtherKPI4";
        public const string OtherKPI5 = "OtherKPI5";
        public const string OtherKPI6 = "OtherKPI6";
        public const string OtherKPI7 = "OtherKPI7";
        public const string OtherKPI8 = "OtherKPI8";
        public const string OtherKPI9 = "OtherKPI9";
        public const string OtherKPI10 = "OtherKPI10";
        public const string OtherKPI11 = "OtherKPI11";
        public const string OtherKPI12 = "OtherKPI12";
        public const string OtherKPI13 = "OtherKPI13";
        public const string OtherKPI14 = "OtherKPI14";
        public const string OtherKPI15 = "OtherKPI15";
        public const string CurrencyFormatString = "$'m";

        public const string FundLevelTemplates = "Fund";
        public const int StartingPageNumber = 1;
        public const string CompanyIdPrefix = "CompanyId_";
        public const string PageNumberPrefix = "PageNumber_";
        public const string CompanyHeading = "companyHeading";
        public const string DisplayNone = "display-none";
        public const int CommentaryModuleId = 1001;
        public const string DealIdAlreadyExists = "Deal with the same ID already exists. Please enter a unique ID";
        public const string ClientCode = "ClientCode";
        public const string AsOfDate = "As of Date";
        public static readonly string[] Quarters = { Quarter1, Quarter2, Quarter3, Quarter4 };
        public const string QuarterPrefix = "Quarter ";
        public static readonly string[] Months = { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" };
        public const string UnConfigured = "Un-Configured";
        public const string CapitalCalls = "CapitalCalls";
        public const string RecallableDistributions = "RecallableDistributions";
        public const string NonRecallableDistributions = "NonRecallableDistributions";
        public const string InvestorCommitments = "InvestorCommitments";
        public const string CashEquivalents = "CashEquivalents";
        public const string NAV = "NAV";
        public const string ManagementFees = "ManagementFees";
        public const string DividendIncome = "DividendIncome";
        public const string RealizedGainsLosses = "RealizedGainsLosses";
        public const string UnrealizedGainsLosses = "UnrealizedGainsLosses";
        public const string InvestmentFundsFV = "InvestmentFundsFV";
        public const string GrossTVPI = "GrossTVPI";
        public const string InterestIncome = "InterestIncome";
        public const string InvestmentFundCost = "InvestmentFundCost";
        public const string OtherIncome = "OtherIncome";
        public const string InterestExpense = "InterestExpense";
        public const string OrganizationCosts = "OrganizationCosts";
        public const string LiabilitiesLongTerm = "LiabilitiesLongTerm";
        public const string LiabilitiesShortTerm = "LiabilitiesShortTerm";
        public const string DataIngestion = "Data Ingestion";
    }
}

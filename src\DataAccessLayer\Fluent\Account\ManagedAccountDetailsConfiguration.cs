using DataAccessLayer.ManagedAccounts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataAccessLayer.Fluent.Account
{
    public class ManagedAccountDetailsConfiguration : IEntityTypeConfiguration<ManagedAccountDetails>
    {
        public void Configure(EntityTypeBuilder<ManagedAccountDetails> builder)
        {
            builder.ToTable("ManagedAccountDetails");

            builder.HasKey(e => e.ManagedAccountID);

            // Configure the primary key to be generated on add only
            builder.Property(e => e.ManagedAccountID)
                .ValueGeneratedOnAdd()
                .HasDefaultValueSql("NEWID()");

            // Configure other properties
            builder.Property(e => e.Domicile)
                .HasMaxLength(200);

            builder.Property(e => e.InvestmentPeriodEndDate)
                .HasMaxLength(100);

            builder.Property(e => e.CommitmentOutstanding)
                .HasMaxLength(100);

            builder.Property(e => e.CommitmentOutstandingCurrency)
                .HasMaxLength(10);

            builder.Property(e => e.BaseCurrency)
                .HasMaxLength(10);

            builder.Property(e => e.InvestmentManager)
                .HasMaxLength(200);

            builder.Property(e => e.Administrator)
                .HasMaxLength(200);

            builder.Property(e => e.Custodian)
                .HasMaxLength(200);

            builder.Property(e => e.LegalCounsel)
                .HasMaxLength(200);

            builder.Property(e => e.LEI)
                .HasMaxLength(50);

            builder.Property(e => e.InvestmentSummary)
                .HasMaxLength(6000);

            // Configure audit fields
            builder.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(e => e.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(e => e.CreatedOn)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(e => e.CreatedBy)
                .IsRequired();
        }
    }
}

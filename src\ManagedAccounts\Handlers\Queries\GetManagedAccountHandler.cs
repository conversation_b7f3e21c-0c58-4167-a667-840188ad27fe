using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Mappers;

namespace ManagedAccounts.Handlers.Queries
{
    /// <summary>
    /// Handler for getting managed accounts by ID
    /// </summary>
    public class GetManagedAccountHandler : IRequestHandler<GetManagedAccountQuery, GetManagedAccountResult>
    {
        private readonly IManagedAccountDetailsService _managedAccountService;
        private readonly ILogger<GetManagedAccountHandler> _logger;

        public GetManagedAccountHandler(
            IManagedAccountDetailsService managedAccountService,
            ILogger<GetManagedAccountHandler> logger)
        {
            _managedAccountService = managedAccountService ?? throw new ArgumentNullException(nameof(managedAccountService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get managed account query
        /// </summary>
        /// <param name="request">The get query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<GetManagedAccountResult> Handle(GetManagedAccountQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting managed account with ID: {Id}", request.Id);

                var account = await _managedAccountService.GetByIdAsync(request.Id);

                if (account == null)
                {
                    _logger.LogWarning("Managed account with ID: {Id} not found", request.Id);
                    return GetManagedAccountResult.Failure($"Managed account with ID {request.Id} not found");
                }

                _logger.LogInformation("Successfully retrieved managed account with ID: {Id}", request.Id);

                var accountDto = ManagedAccountMapper.ToResponseDto(account);
                return GetManagedAccountResult.Success(accountDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed account with ID: {Id}", request.Id);
                return GetManagedAccountResult.Failure($"Failed to get managed account: {ex.Message}");
            }
        }
    }
}

﻿using API.Filters;
using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.PortfolioCompany;
using Contract.Utility;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Controllers.DashboardTracker
{
    [Route("api/")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class DashboardTrackerController : ControllerBase
    {
        private readonly IDashboardTrackerService _dashboardTrackerService;
        private readonly IHelperService _helperService;
        public DashboardTrackerController(IDashboardTrackerService dashboardTrackerService, IHelperService helperService)
        {
            _dashboardTrackerService = dashboardTrackerService;
            _helperService = helperService;
        }

        [HttpGet("dashboard-tracker/get")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> GetCompanies()
        {

            var result = await _dashboardTrackerService.GetPortfolioCompanies(
                new PortfolioCompanyFilter
                {
                    CreatedBy = _helperService.GetCurrentUserId(User)
                });

            return Ok(result);
        }

        [HttpPost("dashboard-tracker/config/save")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> SaveDashboardTrackerConfig([FromBody] DashboardTrackerConfigDto dto)
        {
            if (!ModelState.IsValid)
            {
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }

            try
            {
                int userId = _helperService.GetCurrentUserId(User);

                // Set audit fields based on whether it's an insert or update
                if (dto.ID.HasValue && dto.ID.Value > 0)
                {
                    dto.ModifiedBy = userId;
                    dto.ModifiedOn = DateTime.UtcNow;
                }
                else
                {
                    dto.CreatedBy = userId;
                    dto.CreatedOn = DateTime.UtcNow;
                }

                int result = await _dashboardTrackerService.SaveDashboardTrackerConfigAsync(dto);

                if (result > 0)
                {
                    string message = dto.ID.HasValue && dto.ID.Value > 0
                        ? "Dashboard tracker configuration updated successfully"
                        : "Dashboard tracker configuration added successfully";
                    return JsonResponse.Create(HttpStatusCode.OK, message, new { id = result });
                }
                else
                {
                    return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
                }
            }
            catch (Exception ex)
            {
                // Log the exception here if you have logging configured
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }
        }

        [HttpPost("dashboard-tracker/table-data")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> GetDashboardTableData([FromBody] PaginationFilter filter)
        {
            filter = filter ?? new PaginationFilter();
            int userId = _helperService.GetCurrentUserId(User);
            var result = await _dashboardTrackerService.GetDashboardTableDataAsync(userId, filter);
            return Ok(result);
        }

        [HttpPost("dashboard-tracker/save-dropdown-values")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> SaveDropdownValues([FromBody] TrackerDropdownValueDto dto)
        {
            if (dto == null || dto.DropdownValueWithType == null || dto.DropdownValueWithType.Count == 0)
                return BadRequest("Invalid input");

            var result = await _dashboardTrackerService.SaveTrackerDropdownValuesAsync(dto);
            if (result)
                return Ok(new { success = true });
            else
                return StatusCode(500, new { success = false, message = "Failed to save dropdown values." });
        }
        [HttpGet("dashboard-tracker/get-column-data")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]

        public async Task<IActionResult> GetAllTrackerConfigs()
        {
            var configs = await _dashboardTrackerService.GetAllTrackerConfigsAsync();
            return Ok(configs);
        }

        [HttpPost("dashboard-tracker/save-cell-values")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> SaveCellValues([FromBody] SaveDashboardCellValuesDto dto)
        {
            if (!ModelState.IsValid)
            {
                return JsonResponse.Create(HttpStatusCode.BadRequest, "Invalid input data");
            }

            if (dto?.CellValues == null || dto.CellValues.Count == 0)
            {
                return JsonResponse.Create(HttpStatusCode.BadRequest, "No cell values provided");
            }

            try
            {
                int userId = _helperService.GetCurrentUserId(User);
                bool result = await _dashboardTrackerService.SaveDashboardCellValuesAsync(dto, userId);

                if (result)
                {
                    return JsonResponse.Create(HttpStatusCode.OK, "Cell values saved successfully");
                }
                else
                {
                    return JsonResponse.Create(HttpStatusCode.InternalServerError, "Failed to save cell values");
                }
            }
            catch (Exception)
            {
                // Log the exception here if you have logging configured
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }
        }

        [HttpDelete("dashboard-tracker/config/delete/{id}")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> DeleteDashboardTrackerConfig(int id)
        {
            var result = await _dashboardTrackerService.DeleteDashboardTrackerConfigAsync(id);
            if (result)
                return Ok(new { success = true });
            else
                return NotFound(new { success = false, message = "Record not found." });
        }

        [HttpGet("dashboard-tracker/get-available-custom-fields")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> GetCustomFieldsforMaptoColumns()
        {
            var result = await _dashboardTrackerService.GetAvailableCustomFields();
            return Ok(result);
        }

        [HttpDelete("dashboard-tracker/delete-columns")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> DeleteDashboardTrackerColumns([FromBody] List<ColumnsDto> columns)
        {
            if (columns == null || columns.Count == 0)
            {
                return BadRequest("No columns provided");
            }
            var result = await _dashboardTrackerService.DeleteDashboardTrackerColumnsAsync(columns);
            if (result)
                return Ok(new { success = true });
            else
                return NotFound(new { success = false, message = "Record not found." });
        }

        [HttpPost("dashboard-tracker/deleted-columns")]
        [AuthorizeUserPermission(Features.DashboardTracker, Actions.canEdit)]
        public async Task<IActionResult> GetDeletedColumns([FromBody] PaginationFilter filter)
        {
            int userId = _helperService.GetCurrentUserId(User);
            var result = await _dashboardTrackerService.GetDeletedColumnsAsync(userId,filter);
            return Ok(result);
        }
    }
}



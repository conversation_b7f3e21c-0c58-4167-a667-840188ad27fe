﻿using API.Controllers.DashboardTracker;
using API.Helpers;
using Contract.Account;
using Contract.PortfolioCompany;
using Contract.Utility;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System.Security.Claims;

namespace DocumentCollection.UnitTest.Controller
{
    public class DashboardTrackerControllerTest
    {
        private readonly Mock<IDashboardTrackerService> _mockService;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly DashboardTrackerController _controller;

        public DashboardTrackerControllerTest()
        {
            _mockService = new Mock<IDashboardTrackerService>();
            _mockHelperService = new Mock<IHelperService>();
            _controller = new DashboardTrackerController(_mockService.Object, _mockHelperService.Object);
        }

        [Fact]
        public async Task GetCompanies_ReturnsOkResult_WithCompanies()
        {
            // Arrange
            var userId = 1;
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 1, CompanyName = "Company A", FundName = "Fund A" },
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 2, CompanyName = "Company B", FundName = "Fund B" }
            };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetPortfolioCompanies(It.IsAny<PortfolioCompanyFilter>())).ReturnsAsync(companies);

            // Act
            var result = await _controller.GetCompanies();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedCompanies = Assert.IsType<List<PortfolioCompanyQueryModel>>(okResult.Value);
            Assert.Equal(2, returnedCompanies.Count);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_InvalidModelState_ReturnsInternalServerError()
        {
            // Arrange
            _controller.ModelState.AddModelError("Name", "Required");
            var dto = new DashboardTrackerConfigDto();

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_Insert_ReturnsOkResult()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(10);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);            
            Assert.Contains("added", ((string)value.Message).ToLower());
            Assert.NotNull(value.Code);            
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_Update_ReturnsOkResult()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { ID = 5, FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(5);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);            
            Assert.Contains("updated", ((string)value.Message).ToLower());
            Assert.NotNull(value.Code);            
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_ServiceReturnsZero_ReturnsInternalServerError()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(0);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);                        
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ThrowsAsync(new System.Exception("error"));

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);                        
        }

        [Fact]
        public async Task GetDashboardTableData_ReturnsOkResult_WithValidData()
        {
            // Arrange
            var userId = 1;
            var filter = new Contract.Utility.PaginationFilter { First = 0, Rows = 100 };
            var tableData = new TableDataResultDto { Success = true, Data = new List<Dictionary<string, object>>(), Columns = new List<ColumnsDto>() };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, filter)).ReturnsAsync(tableData);

            // Act
            var result = await _controller.GetDashboardTableData(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(tableData, okResult.Value);
        }

        [Fact]
        public async Task GetDashboardTableData_NullFilter_UsesDefault()
        {
            // Arrange
            var userId = 1;
            var tableData = new TableDataResultDto { Success = true, TotalRecords = 100, Columns = new List<ColumnsDto>(), Data = new List<Dictionary<string, object>>() };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, It.IsAny<Contract.Utility.PaginationFilter>())).ReturnsAsync(tableData);

            // Act
            var result = await _controller.GetDashboardTableData(null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(tableData, okResult.Value);
        }

        [Fact]
        public async Task GetDashboardTableData_ServiceReturnsNull_ReturnsOkWithNull()
        {
            // Arrange
            var userId = 1;
            var filter = new Contract.Utility.PaginationFilter { First = 0, Rows = 100 };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, filter)).ReturnsAsync((TableDataResultDto)null);

            // Act
            var result = await _controller.GetDashboardTableData(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Null(okResult.Value);
        }

        [Fact]
        public async Task SaveDropdownValues_ValidDto_ReturnsOkResult()
        {
            // Arrange
            var dropdownlist = new List<TrackerDropdownValueWithType> {
                new TrackerDropdownValueWithType
                {
                    DropdownValues = new List<string> { "A", "B" },
                    DropdownType = 1
                }
            };

            var dto = new TrackerDropdownValueDto { TrackerFieldId = 2, DropdownValueWithType = dropdownlist };
            
            _mockService.Setup(s => s.SaveTrackerDropdownValuesAsync(dto)).ReturnsAsync(true);

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            // The returned value is an anonymous object: { success = true }
            var value = okResult.Value;
            Assert.NotNull(value);
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.True((bool)successProperty.GetValue(value));
        }

        [Fact]
        public async Task SaveDropdownValues_NullDto_ReturnsBadRequest()
        {
            // Arrange
            TrackerDropdownValueDto dto = null;

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task SaveDropdownValues_EmptyDropdownValues_ReturnsBadRequest()
        {
            // Arrange
            var dropdownlist = new List<TrackerDropdownValueWithType> {
                new TrackerDropdownValueWithType
                {
                    DropdownValues = new List<string>(),
                    DropdownType = 1
                }
            };

            var dto = new TrackerDropdownValueDto { DropdownValueWithType = dropdownlist };


            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert            
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, objectResult.StatusCode);
        }

        [Fact]
        public async Task SaveDropdownValues_ServiceReturnsFalse_ReturnsInternalServerError()
        {
            // Arrange
            var dropdownlist = new List<TrackerDropdownValueWithType> {
                new TrackerDropdownValueWithType
                {
                    DropdownValues = new List<string> { "A" },
                    DropdownType = 1
                }
            };

            var dto = new TrackerDropdownValueDto { DropdownValueWithType = dropdownlist };
            
            _mockService.Setup(s => s.SaveTrackerDropdownValuesAsync(dto)).ReturnsAsync(false);

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, objectResult.StatusCode);
        }
        [Fact]
        public async Task GetAllTrackerConfigs_ReturnsOkResult_WithConfigs()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfigDto>
    {
        new DashboardTrackerConfigDto
        {
            ID = 1,
            FieldType = 1,
            FieldTypeName = "Data",
            DataType = 4,
            DataTypeName = "Dropdown",
            Name = "Test Field",
            StartPeriod = "2023",
            MapTo = 2,
            DropdownList = new List<string> { "A", "B" }
        },
        new DashboardTrackerConfigDto
        {
            ID = 2,
            FieldType = 2,
            FieldTypeName = "TimeSeries",
            DataType = 1,
            DataTypeName = "Text",
            Name = "Another Field",
            StartPeriod = "2022",
            MapTo = 3
        }
    };
            _mockService.Setup(s => s.GetAllTrackerConfigsAsync()).ReturnsAsync(configs);

            // Act
            var result = await _controller.GetAllTrackerConfigs();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedConfigs = Assert.IsType<List<DashboardTrackerConfigDto>>(okResult.Value);
            Assert.Equal(2, returnedConfigs.Count);
            Assert.Equal("Test Field", returnedConfigs[0].Name);
            Assert.Equal("Dropdown", returnedConfigs[0].DataTypeName);
            Assert.Equal("A", returnedConfigs[0].DropdownList[0]);
        }

        [Fact]
        public async Task GetAllTrackerConfigs_ReturnsOkResult_EmptyList()
        {
            // Arrange
            _mockService.Setup(s => s.GetAllTrackerConfigsAsync()).ReturnsAsync(new List<DashboardTrackerConfigDto>());

            // Act
            var result = await _controller.GetAllTrackerConfigs();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedConfigs = Assert.IsType<List<DashboardTrackerConfigDto>>(okResult.Value);
            Assert.Empty(returnedConfigs);
        }

        [Fact]
        public async Task SaveCellValues_WithValidData_ReturnsOk()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Test Value"
                    }
                }
            };

            _mockService.Setup(x => x.SaveDashboardCellValuesAsync(dto, It.IsAny<int>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SaveCellValues(dto);

            // Assert
            Assert.IsType<JsonResult>(result);
            var jsonResult = result as JsonResult;
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task SaveCellValues_WithInvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto();
            _controller.ModelState.AddModelError("CellValues", "Required");

            // Act
            var result = await _controller.SaveCellValues(dto);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveCellValues_WithNullDto_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.SaveCellValues(null);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveCellValues_WithEmptyCellValues_ReturnsBadRequest()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>()
            };

            // Act
            var result = await _controller.SaveCellValues(dto);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveCellValues_WithServiceFailure_ReturnsInternalServerError()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Test Value"
                    }
                }
            };

            _mockService.Setup(x => x.SaveDashboardCellValuesAsync(dto, It.IsAny<int>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.SaveCellValues(dto);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveCellValues_WithException_ReturnsInternalServerError()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Test Value"
                    }
                }
            };

            _mockService.Setup(x => x.SaveDashboardCellValuesAsync(dto, It.IsAny<int>()))
                .ThrowsAsync(new Exception("Service error"));

            // Act
            var result = await _controller.SaveCellValues(dto);

            // Assert
            Assert.IsType<JsonResult>(result);
        }


        [Fact]
        public async Task DeleteDashboardTrackerConfig_RecordExists_ReturnsOkResult()
        {
            // Arrange
            int id = 10;
            _mockService.Setup(s => s.DeleteDashboardTrackerConfigAsync(id)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteDashboardTrackerConfig(id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var value = okResult.Value;
            Assert.NotNull(value);
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.True((bool)successProperty.GetValue(value));
        }

        [Fact]
        public async Task DeleteDashboardTrackerConfig_RecordDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            int id = 99;
            _mockService.Setup(s => s.DeleteDashboardTrackerConfigAsync(id)).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteDashboardTrackerConfig(id);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var value = notFoundResult.Value;
            Assert.NotNull(value);
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.False((bool)successProperty.GetValue(value));
            var messageProperty = value.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            Assert.Equal("Record not found.", messageProperty.GetValue(value));
        }
        [Fact]
        public async Task DeleteDashboardTrackerConfig_ConfigAndDropdownValuesExist_ReturnsOkResult()
        {
            // Arrange
            int id = 10;
            _mockService.Setup(s => s.DeleteDashboardTrackerConfigAsync(id)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteDashboardTrackerConfig(id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var value = okResult.Value;
            Assert.NotNull(value);
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.True((bool)successProperty.GetValue(value));
        }
        [Fact]
        public async Task GetCustomFieldsforMaptoColumns_ReturnsOkResult_WithCustomFields()
        {
            // Arrange
            var customFields = new List<ColumnAlias> { new ColumnAlias() {
                DisplayText = "Field1",
                Value = "1",
                Type = 1

            },new ColumnAlias() {
                DisplayText = "Field2",
                Value = "1",
                Type = 1

            },new ColumnAlias() {
                DisplayText = "Field3",
                Value = "1",
                Type = 1

            }, };
            _mockService.Setup(s => s.GetAvailableCustomFields()).ReturnsAsync(customFields);

            // Act
            var result = await _controller.GetCustomFieldsforMaptoColumns();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedFields = Assert.IsType<List<ColumnAlias>>(okResult.Value);
            var resultDisplayText = returnedFields.Select(i => i.DisplayText).ToList();
            Assert.Equal(3, returnedFields.Count);
            Assert.Contains("Field1", resultDisplayText);
            Assert.Contains("Field2", resultDisplayText);
        }

        [Fact]
        public async Task GetCustomFieldsforMaptoColumns_ReturnsOkResult_WithEmptyList()
        {
            // Arrange
            _mockService.Setup(s => s.GetAvailableCustomFields()).ReturnsAsync(new List<ColumnAlias>());

            // Act
            var result = await _controller.GetCustomFieldsforMaptoColumns();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedFields = Assert.IsType<List<ColumnAlias>>(okResult.Value);
            Assert.Empty(returnedFields);
        }

        [Fact]
        public async Task GetCustomFieldsforMaptoColumns_ReturnsOkResult_WithNull()
        {
            // Arrange
            _mockService.Setup(s => s.GetAvailableCustomFields()).ReturnsAsync((List<ColumnAlias>)null);

            // Act
            var result = await _controller.GetCustomFieldsforMaptoColumns();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Null(okResult.Value);
        }

        // ==================== DELETE DASHBOARD TRACKER COLUMNS TESTS ====================

        [Fact]
        public async Task DeleteDashboardTrackerColumns_ValidColumns_ReturnsOkResult()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 1,
                    IsTimeSeries = true,
                    DataType = (int)DataTypeEnum.Number,
                    TimeSeriesID = "2023-Q1"
                },
                new ColumnsDto
                {
                    ID = 2,
                    IsTimeSeries = false,
                    DataType = (int)DataTypeEnum.Text,
                    Name = "Revenue"
                }
            };

            _mockService.Setup(s => s.DeleteDashboardTrackerColumnsAsync(columns)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteDashboardTrackerColumns(columns);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var value = okResult.Value;
            Assert.NotNull(value);
            
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.True((bool)successProperty.GetValue(value));
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumns_EmptyColumns_ReturnsBadRequest()
        {
            // Arrange
            var columns = new List<ColumnsDto>();
            _mockService.Setup(s => s.DeleteDashboardTrackerColumnsAsync(columns)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteDashboardTrackerColumns(columns);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var value = badRequestResult.Value;
            Assert.Equal("No columns provided", value);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumns_ServiceReturnsFalse_ReturnsNotFound()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 1,
                    IsTimeSeries = true,
                    DataType = (int)DataTypeEnum.Number,
                    TimeSeriesID = "2023-Q1"
                }
            };

            _mockService.Setup(s => s.DeleteDashboardTrackerColumnsAsync(columns)).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteDashboardTrackerColumns(columns);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var value = notFoundResult.Value;
            Assert.NotNull(value);
            
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.False((bool)successProperty.GetValue(value));
            
            var messageProperty = value.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            Assert.Equal("Record not found.", messageProperty.GetValue(value));
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumns_ServiceThrowsException_ThrowsException()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 1,
                    IsTimeSeries = true,
                    DataType = (int)DataTypeEnum.Number,
                    TimeSeriesID = "2023-Q1"
                }
            };

            _mockService.Setup(s => s.DeleteDashboardTrackerColumnsAsync(columns))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _controller.DeleteDashboardTrackerColumns(columns));
            Assert.Equal("Database connection failed", exception.Message);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumns_NullColumns_ReturnsBadRequest()
        {
            // Arrange
            List<ColumnsDto> columns = null;

            // Act
            var result = await _controller.DeleteDashboardTrackerColumns(columns);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var value = badRequestResult.Value;
            Assert.Equal("No columns provided", value);
        }

        // ==================== GET DELETED COLUMNS TESTS ====================

        [Fact]
        public async Task GetDeletedColumns_ValidFilter_ReturnsOkResult()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = true,
                Data = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "ConfigID", 1 },
                        { "ColumnName", "Revenue" },
                        { "DeletedColumnID", "2023-Q1" }
                    }
                },
                Columns = new List<ColumnsDto>
                {
                    new ColumnsDto
                    {
                        ID = 1,
                        Name = "Revenue",
                        IsTimeSeries = true,
                        TimeSeriesID = "2023-Q1"
                    }
                },
                TotalRecords = 1
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter)).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.True(returnedData.Success);
            Assert.NotNull(returnedData.Data);
            Assert.NotNull(returnedData.Columns);
            Assert.Equal(1, returnedData.TotalRecords);
            Assert.Single(returnedData.Data);
            Assert.Single(returnedData.Columns);
        }

        [Fact]
        public async Task GetDeletedColumns_EmptyResult_ReturnsOkResult()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = true,
                Data = new List<Dictionary<string, object>>(),
                Columns = new List<ColumnsDto>(),
                TotalRecords = 0
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter)).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.True(returnedData.Success);
            Assert.Empty(returnedData.Data);
            Assert.Empty(returnedData.Columns);
            Assert.Equal(0, returnedData.TotalRecords);
        }

        [Fact]
        public async Task GetDeletedColumns_ServiceFailure_ReturnsOkResult()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = false,
                Data = new List<Dictionary<string, object>>(),
                Columns = new List<ColumnsDto>(),
                TotalRecords = 0
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter)).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.False(returnedData.Success);
            Assert.Empty(returnedData.Data);
            Assert.Empty(returnedData.Columns);
            Assert.Equal(0, returnedData.TotalRecords);
        }

        [Fact]
        public async Task GetDeletedColumns_ServiceThrowsException_ThrowsException()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _controller.GetDeletedColumns(filter));
            Assert.Equal("Database connection failed", exception.Message);
        }

        [Fact]
        public async Task GetDeletedColumns_NullFilter_UsesDefaultFilter()
        {
            // Arrange
            PaginationFilter filter = null;
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = true,
                Data = new List<Dictionary<string, object>>(),
                Columns = new List<ColumnsDto>(),
                TotalRecords = 0
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId,It.IsAny<PaginationFilter>())).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.True(returnedData.Success);
            _mockService.Verify(s => s.GetDeletedColumnsAsync(userId, It.IsAny<PaginationFilter>()), Times.Once);
        }

        [Fact]
        public async Task GetDeletedColumns_WithPagination_AppliesFilterCorrectly()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 5, First = 10 }; // Page 3, 5 rows per page
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = true,
                Data = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object> { { "ConfigID", 1 }, { "ColumnName", "Revenue" } },
                    new Dictionary<string, object> { { "ConfigID", 2 }, { "ColumnName", "EBITDA" } },
                    new Dictionary<string, object> { { "ConfigID", 3 }, { "ColumnName", "NetIncome" } }
                },
                Columns = new List<ColumnsDto>
                {
                    new ColumnsDto { ID = 1, Name = "Revenue" },
                    new ColumnsDto { ID = 2, Name = "EBITDA" },
                    new ColumnsDto { ID = 3, Name = "NetIncome" }
                },
                TotalRecords = 15
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter)).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.True(returnedData.Success);
            Assert.Equal(15, returnedData.TotalRecords);
            Assert.Equal(3, returnedData.Data.Count);
            Assert.Equal(3, returnedData.Columns.Count);
            
            // Verify that the service was called with the correct filter
            _mockService.Verify(s => s.GetDeletedColumnsAsync(userId, filter), Times.Once);
        }

        [Fact]
        public async Task GetDeletedColumns_WithTimeSeriesColumns_ReturnsProperStructure()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var deletedColumnsResult = new TableDataResultDto
            {
                Success = true,
                Data = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "ConfigID", 1 },
                        { "ColumnName", "Revenue" },
                        { "DeletedColumnID", "2023-Q1" },
                        { "IsTimeSeries", true }
                    }
                },
                Columns = new List<ColumnsDto>
                {
                    new ColumnsDto
                    {
                        ID = 1,
                        Name = "2023-Q1 - Revenue",
                        IsTimeSeries = true,
                        TimeSeriesID = "2023-Q1"
                    }
                },
                TotalRecords = 1
            };

            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDeletedColumnsAsync(userId, filter)).ReturnsAsync(deletedColumnsResult);

            // Act
            var result = await _controller.GetDeletedColumns(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedData = Assert.IsType<TableDataResultDto>(okResult.Value);
            
            Assert.True(returnedData.Success);
            Assert.Single(returnedData.Columns);
            
            var timeSeriesColumn = returnedData.Columns.First();
            Assert.True(timeSeriesColumn.IsTimeSeries);
            Assert.Equal("2023-Q1", timeSeriesColumn.TimeSeriesID);
            Assert.Contains("Revenue", timeSeriesColumn.Name);
        }
    }
}

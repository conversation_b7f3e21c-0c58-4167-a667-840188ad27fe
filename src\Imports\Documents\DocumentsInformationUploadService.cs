using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.Common;
using Contract.BulkUpload;
using Contract.Documents;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.UnitOfWork;
using Imports.Helpers;
using Imports.Kpi;
using Imports.Services;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using Contract.PortfolioCompany;
using DapperRepository.Constants;
using Shared;
namespace Imports.Documents;
public class DocumentsInformationUploadService(IUnitOfWork unitOfWork, IDapperGenericRepository dapperGenericRepository, ILogger<DocumentsInformationUploadService> logger, IBulkUpload bulkUpload) : IDocumentsInformationUploadService
{
    private readonly IUnitOfWork _unitOfWork = unitOfWork;
    private readonly IBulkUpload _bulkUpload = bulkUpload;
    private readonly IDapperGenericRepository _dapperGenericRepository = dapperGenericRepository;
    private readonly ILogger<DocumentsInformationUploadService> _logger = logger;
    public ImportService OperationalKpiService;

    /// <summary>
    /// Processes the default template for documents.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="connectionString">The connection string.</param>
    /// <param name="moduleStatus">The list of module statuses.</param>
    /// <param name="fullPath">The full path of the document.</param>
    /// <param name="package">The Excel package.</param>
    /// <returns>The updated list of module statuses.</returns>
    public async Task<List<Status>> ProcessDefaultTemplate(DocumentsInformationModel documentsInformationModel, List<Status> moduleStatus, string fullPath, ExcelPackage package, int userId)
    {
        _logger.LogInformation("ProcessDefaultTemplate started.");
        Dictionary<string, (KpiModuleType, Sheet)> worksheetMappings = GetWorkSheetMappings();
        moduleStatus = await ProcessKpisUpload(documentsInformationModel, documentsInformationModel.ConnectionString, moduleStatus, fullPath, userId).ConfigureAwait(false);
        _logger.LogInformation("ProcessDefaultTemplate completed.");
        return moduleStatus;
    }

    /// <summary>
    /// Processes the KPIs upload for the given documents information.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="connectionString">The connection string.</param>
    /// <param name="moduleStatus">The list of status.</param>
    /// <param name="fullPath">The full path of the document.</param>
    /// <param name="userId">The user ID.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of status.</returns>
    private async Task<List<Status>> ProcessKpisUpload(DocumentsInformationModel documentsInformationModel, string connectionString, List<Status> moduleStatus, string fullPath, int userId)
    {
        Dictionary<string, KpiModuleType> kpiModuleMap = GetCustomAndOtherKpiTypes();
        switch (documentsInformationModel.ModuleName)
        {
            case Constants.BulkUploadTradingKpi:
                DocumentUploadModel documentUploadModelTrading = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.TradingRecords, Shared.Constants.TradingRecords_KPI, userId);
                moduleStatus = await ProcessTradingKpi(documentsInformationModel, documentUploadModelTrading);
                break;
            case Constants.BulkUploadInvestmentKpi:
                DocumentUploadModel documentUploadModelInvestment = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.Investment, Shared.Constants.Investment_KPI, userId);
                moduleStatus = await ProcessInvestmentKpi(documentsInformationModel, documentUploadModelInvestment);
                break;

            case Constants.BulkUploadImpactKpi:
                DocumentUploadModel documentUploadModelImpact = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.Impact, Shared.Constants.Impact_KPI, userId);
                moduleStatus = await ProcessImpactKpi(documentsInformationModel, documentUploadModelImpact);
                break;

            case Constants.MCapTable:
                DocumentUploadModel documentUploadModelCapTable = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.Impact, Constants.Impact_KPI, userId);
                moduleStatus = await _bulkUpload.ProcessCapTableKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModelCapTable, TableName.None));
                break;
            case Constants.FundKeyPerformanceIndicatorConfig:
                DocumentUploadModel documentUpload = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, 0, Constants.FundKPIs, userId);
                moduleStatus = await _bulkUpload.ProcessFundKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUpload, TableName.FundKpis));
                break;
            case Constants.FundFinancialsConfig:
                DocumentUploadModel documentUploadFundFinancials = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, 0, Constants.FundFinancials, userId);
                moduleStatus = await _bulkUpload.ProcessFundKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadFundFinancials, TableName.FundFinancials));
                break;
            case Constants.MOtherCapTable:
                DocumentUploadModel documentUploadModelOtherCapTable = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.OtherCapTable1, Constants.OtherCapTable, userId);
                moduleStatus = await _bulkUpload.ProcessCapTableKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModelOtherCapTable, TableName.None), true);
                break;
            case Constants.KpiFinancials:
                var moduleTypes = new List<KpiModuleType>();
                await ValidateFinancialsExportAccess(documentsInformationModel, userId, moduleTypes);
                _logger.LogInformation("Start:ValidateFinancialsExportAccess : " + string.Join(",", moduleTypes));
                foreach (var moduleType in moduleTypes)
                {
                    DocumentUploadModel documentUploadModel = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)moduleType, Shared.Constants.KpiFinancials, userId);
                    var result = await ProcessFinancialKpis(documentsInformationModel, documentUploadModel, TableName.ProfitAndLoss, moduleTypes).ConfigureAwait(false);
                    if (result != null)
                        moduleStatus.AddRange(result);
                }
                break;
            case Constants.BulkUploadCreditKpi:
                DocumentUploadModel documentUploadModelCredit = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.CreditKPI, Shared.Constants.Credit_KPI, userId);
                _logger.LogInformation("Start:ProcessCreditKpi" + DateTime.Now);
                moduleStatus = await ProcessCreditKpi(documentsInformationModel, documentUploadModelCredit);
                _logger.LogInformation("End:ProcessCreditKpi" + DateTime.Now);
                break;
            case Constants.BulkUploadCompanyKpi:
                DocumentUploadModel documentUploadModelCompany = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.Company, Constants.Company_KPI, userId);
                _logger.LogInformation("Start:ProcessCompanyKpi" + DateTime.Now);
                moduleStatus = await ProcessCompanyKpi(documentsInformationModel, documentUploadModelCompany) ?? moduleStatus;
                _logger.LogInformation("End:ProcessCompanyKpi" + DateTime.Now);
                break;
            case Constants.BulkUploadOperationalKpi:
                DocumentUploadModel documentUploadModelOperational = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.Operational, Constants.OperationalKPIss, userId);
                _logger.LogInformation("Start:ProcessOperationalKpi" + DateTime.Now);
                moduleStatus = await ProcessOperationalKpi(documentsInformationModel, documentUploadModelOperational) ?? moduleStatus;
                _logger.LogInformation("End:ProcessOperationalKpi" + DateTime.Now);
                break;
            case Constants.MonthlyReport:
                DocumentUploadModel documentUploadModelMonthlyReport = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)KpiModuleType.MonthlyReport, Constants.MonthlyReportSheet, userId);
                _logger.LogInformation("Start:Process Monthly Report Upload" + DateTime.Now);
                moduleStatus = await ProcessMonthlyReport(documentsInformationModel, documentUploadModelMonthlyReport) ?? moduleStatus;
                _logger.LogInformation("End:Process Monthly Report Upload" + DateTime.Now);
                break;
            default:
                if (kpiModuleMap.TryGetValue(documentsInformationModel.ModuleName, out var kpiModuleType))
                {
                    DocumentUploadModel documentUploadModel = DocumentHelper.GetDocumentUploadModel(connectionString, fullPath, null, (int)kpiModuleType, documentsInformationModel.ModuleName, userId);
                    _logger.LogInformation("Start:Process {ModuleName} {Time}", documentsInformationModel.ModuleName, DateTime.Now);
                    moduleStatus = await ProcessCustomAndOtherKpi(documentsInformationModel, documentUploadModel) ?? moduleStatus;
                    _logger.LogInformation("End:Process {ModuleName} {Time}", documentsInformationModel.ModuleName, DateTime.Now);
                }
                else
                {
                    _logger.LogInformation("Module name not recognized.");
                }
                break;
        }
        return moduleStatus;
    }

    /// <summary>
    /// GetCustomAndOtherKpiTypes
    /// </summary>
    /// <returns></returns>
    private static Dictionary<string, KpiModuleType> GetCustomAndOtherKpiTypes()
    {
        return new Dictionary<string, KpiModuleType>
            {
                { Constants.Custom_Table1, KpiModuleType.CustomTable1 },
                { Constants.Custom_Table2, KpiModuleType.CustomTable2 },
                { Constants.Custom_Table3, KpiModuleType.CustomTable3 },
                { Constants.Custom_Table4, KpiModuleType.CustomTable4 },
                { Constants.Custom_Table5, KpiModuleType.CustomTable5 },
                { Constants.Custom_Table6, KpiModuleType.CustomTable6 },
                { Constants.Custom_Table7, KpiModuleType.CustomTable7 },
                { Constants.Custom_Table8, KpiModuleType.CustomTable8 },
                { Constants.Custom_Table9, KpiModuleType.CustomTable9 },
                { Constants.Other_KPI1, KpiModuleType.OtherKPI1 },
                { Constants.Other_KPI2, KpiModuleType.OtherKPI2 },
                { Constants.Other_KPI3, KpiModuleType.OtherKPI3 },
                { Constants.Other_KPI4, KpiModuleType.OtherKPI4 },
                { Constants.Other_KPI5, KpiModuleType.OtherKPI5 },
                { Constants.Other_KPI6, KpiModuleType.OtherKPI6 },
                { Constants.Other_KPI7, KpiModuleType.OtherKPI7 },
                { Constants.Other_KPI8, KpiModuleType.OtherKPI8 },
                { Constants.Other_KPI9, KpiModuleType.OtherKPI9 },
                { Constants.Other_KPI10, KpiModuleType.OtherKPI10 },
                { Constants.Other_KPI11, KpiModuleType.OtherKPI11 },
                { Constants.Other_KPI12, KpiModuleType.OtherKPI12 },
                { Constants.Other_KPI13, KpiModuleType.OtherKPI13 },
                { Constants.Other_KPI14, KpiModuleType.OtherKPI14 },
                { Constants.Other_KPI15, KpiModuleType.OtherKPI15 }
            };
    }

    /// <summary>
    /// Validates access to financials export based on user permissions and updates the module types list accordingly.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model containing company ID.</param>
    /// <param name="userId">The ID of the user whose access is being validated.</param>
    /// <param name="moduleTypes">A list of KpiModuleTypes to be updated based on access permissions.</param>
    private async Task ValidateFinancialsExportAccess(DocumentsInformationModel documentsInformationModel, int userId, List<KpiModuleType> moduleTypes)
    {
        // Retrieve the feature ID for the PortfolioCompany feature
        var featuresData = await _unitOfWork.FeatureRepository.FindFirstAsync(x => x.Feature == Constants.PortfolioCompany);
        var featureId = featuresData.FeatureId;
        // Query for sub-feature access permissions
        var permissions = await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(
            SqlConstants.ProcGetPcSubFeaturePermissions,
            new { UserId = userId, documentsInformationModel.CompanyId, FeatureId = featureId }
        );

        // Filter for relevant financial data permissions
        var relevantPermissions = permissions
            .Where(permission => permission.CanImport && (
                permission.ModuleId == (int)KpiModuleType.ProfitAndLoss ||
                permission.ModuleId == (int)KpiModuleType.BalanceSheet ||
                permission.ModuleId == (int)KpiModuleType.CashFlow))
            .ToList();

        // Configuration mapping from PageConfigName to KpiModuleType
        var configToModuleType = new Dictionary<string, KpiModuleType>
            {
                { Constants.ProfitLossConfigName, KpiModuleType.ProfitAndLoss },
                { Constants.BalanceSheetConfigName, KpiModuleType.BalanceSheet },
                { Constants.CashflowConfigName, KpiModuleType.CashFlow }
            };

        // Update moduleTypes based on permissions
        foreach (var permission in relevantPermissions)
        {
            if (configToModuleType.TryGetValue(permission.PageConfigName, out var kpiModuleType))
            {
                moduleTypes.Add(kpiModuleType);
            }
        }
    }

    private static Dictionary<string, (KpiModuleType, Sheet)> GetWorkSheetMappings()
    {
        /// <summary>
        /// This dictionary contains mappings between sheet names and their corresponding KpiModuleType and FinancialKPISheets.
        /// </summary>
        var worksheetMappings = new Dictionary<string, (KpiModuleType, Sheet)>
        {
            { Shared.Constants.BulkUploadCreditKpi, (KpiModuleType.CreditKPI, Sheet.CreditKPI) },
            { Shared.Constants.BulkUploadTradingKpi, (KpiModuleType.TradingRecords, Sheet.TradingRecords) },
            { Shared.Constants.BulkUploadProfitLoss, (KpiModuleType.ProfitAndLoss, Sheet.ProfitLoss) },
            { Shared.Constants.BulkUploadBalanceSheet, (KpiModuleType.BalanceSheet, Sheet.BalanceSheet) },
            { Shared.Constants.BulkUploadCashFlow, (KpiModuleType.CashFlow, Sheet.CashFlow) },
            { Shared.Constants.BulkUploadOperationalKpi, (KpiModuleType.Operational, Sheet.OperationalKPI) },
            { Shared.Constants.BulkUploadInvestmentKpi, (KpiModuleType.Investment, Sheet.InvestmentKPI) },
            { Shared.Constants.BulkUploadCompanyKpi, (KpiModuleType.Company, Sheet.CompanyKPI) },
             { Shared.Constants.BulkUploadImpactKpi, (KpiModuleType.Impact, Sheet.ImpactKPI) },
            // Add more mappings for other sheet names if needed
        };
        return worksheetMappings;
    }
    /// <summary>
    /// Processes the investment KPI for the given documents information and document upload model.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="documentUploadModel">The document upload model.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of status.</returns>
    private async Task<List<Status>> ProcessInvestmentKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.InvestmentKPI));
    }
    private async Task<List<Status>> ProcessTradingKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.MasterKpis));
    }
    /// <summary>
    /// Processes the Impact KPI for the given documents information and document upload model.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="documentUploadModel">The document upload model.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of status.</returns>
    private async Task<List<Status>> ProcessImpactKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.ImpactKPI));
    }

    /// <summary>
    /// Processes the financial KPIs for the given documents information and document upload model.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="documentUploadModel">The document upload model.</param>
    /// <param name="tableName">The table name.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    /// <param name="moduleTypes">The list of module types.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of status.</returns>
    private async Task<List<Status>> ProcessFinancialKpis(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel, TableName tableName, List<KpiModuleType> moduleTypes)
    {
        var bulkUploadModel = DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, tableName);
        bulkUploadModel.IsFinancial = true;
        return await _bulkUpload.ProcessFinancialKpis(bulkUploadModel, moduleTypes);
    }
    /// <summary>
    /// Gets the table name based on the provided sheet name and decrypted user ID.
    /// </summary>
    /// <param name="sName">The sheet name.</param>
    /// <returns>The table name.</returns>
    public static TableName GetTableName(string sName)
    {
        var financialKpiSheet = Enum.GetValues(typeof(FinancialKPISheets))
                                    .Cast<FinancialKPISheets>()
                                    .FirstOrDefault(sheet => string.Equals(sName, EnumHelper.GetEnumDescription(sheet).ToLower(), StringComparison.OrdinalIgnoreCase));

        return financialKpiSheet switch
        {
            FinancialKPISheets.ProfitLoss => TableName.ProfitAndLoss,
            FinancialKPISheets.BalanceSheet => TableName.BalanceSheet,
            FinancialKPISheets.CashFlow => TableName.CashFlow,
            _ => TableName.None,
        };
    }
    private async Task<List<Status>> ProcessCreditKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.MasterKpis));
    }

    /// <summary>
    /// ProcessCustomAndOtherKpi
    /// </summary>
    /// <param name="documentsInformationModel"></param>
    /// <param name="documentUploadModel"></param>
    /// <returns></returns>
    private async Task<List<Status>> ProcessCustomAndOtherKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.MasterKpis));
    }
    /// <summary>
    /// Processes the company KPI for the given documents information and document upload model.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="documentUploadModel">The document upload model.</param>
    /// <returns>A list of status indicating the result of the process.</returns>
    private async Task<List<Status>> ProcessCompanyKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.CompanyKPI));
    }
    /// <summary>
    /// Processes the operational KPI for the given documents information and document upload model.
    /// </summary>
    /// <param name="documentsInformationModel">The documents information model.</param>
    /// <param name="documentUploadModel">The document upload model.</param>
    /// <returns>A list of status indicating the result of the process.</returns>
    private async Task<List<Status>> ProcessOperationalKpi(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.OperationalKPI));
    }
    /// <summary>
    /// ProcessMonthlyReport
    /// </summary>
    /// <param name="documentsInformationModel"></param>
    /// <param name="documentUploadModel"></param>
    /// <returns></returns>
    private async Task<List<Status>> ProcessMonthlyReport(DocumentsInformationModel documentsInformationModel, DocumentUploadModel documentUploadModel)
    {
        return await _bulkUpload.ProcessKpi(DocumentHelper.CreateBulkUploadModel(documentsInformationModel, documentUploadModel, TableName.MonthlyReport));
    }
}


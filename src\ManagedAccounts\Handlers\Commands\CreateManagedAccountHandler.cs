using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Commands;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using DataAccessLayer.ManagedAccounts;

namespace ManagedAccounts.Handlers.Commands
{
    /// <summary>
    /// Handler for creating managed accounts
    /// </summary>
    public class CreateManagedAccountHandler : IRequestHandler<CreateManagedAccountCommand, CreateManagedAccountResult>
    {
        private readonly IManagedAccountDetailsService _managedAccountService;
        private readonly ILogger<CreateManagedAccountHandler> _logger;

        public CreateManagedAccountHandler(
            IManagedAccountDetailsService managedAccountService,
            ILogger<CreateManagedAccountHandler> logger)
        {
            _managedAccountService = managedAccountService ?? throw new ArgumentNullException(nameof(managedAccountService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the create managed account command
        /// </summary>
        /// <param name="request">The create command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<CreateManagedAccountResult> Handle(CreateManagedAccountCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating new managed account with name: {ManagedAccountName}", request.ManagedAccountName);

                // Check if managed account name already exists
                var isDuplicate = await _managedAccountService.CheckDuplicateNameAsync(request.ManagedAccountName);
                if (isDuplicate)
                {
                    _logger.LogWarning("Managed account with name '{ManagedAccountName}' already exists", request.ManagedAccountName);
                    return CreateManagedAccountResult.Failure($"A managed account with the name '{request.ManagedAccountName}' already exists.");
                }

                // Map command to entity
                var managedAccountDetails = new ManagedAccountDetails
                {
                    ManagedAccountName = request.ManagedAccountName,
                    Domicile = request.Domicile,
                    CommencementDate = request.CommencementDate,
                    InvestmentPeriodEndDate = request.InvestmentPeriodEndDate,
                    MaturityDate = request.MaturityDate,
                    CommitmentOutstanding = request.CommitmentOutstanding,
                    CommitmentOutstandingCurrency = request.CommitmentOutstandingCurrency,
                    BaseCurrency = request.BaseCurrency,
                    InvestmentManager = request.InvestmentManager,
                    Administrator = request.Administrator,
                    Custodian = request.Custodian,
                    LegalCounsel = request.LegalCounsel,
                    LEI = request.LEI,
                    InvestmentSummary = request.InvestmentSummary,
                    CreatedBy = request.CreatedBy
                };

                // Create the managed account
                var id = await _managedAccountService.CreateAsync(managedAccountDetails);

                _logger.LogInformation("Successfully created managed account with ID: {Id}", id);

                return CreateManagedAccountResult.Success(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating managed account with name: {ManagedAccountName}", request.ManagedAccountName);
                return CreateManagedAccountResult.Failure($"Failed to create managed account: {ex.Message}");
            }
        }
    }
}

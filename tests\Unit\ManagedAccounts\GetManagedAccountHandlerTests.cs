using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using ManagedAccounts.Handlers.Queries;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.UnitTest
{
    public class GetManagedAccountHandlerTests
    {
        private readonly Mock<IManagedAccountDetailsService> _mockService;
        private readonly Mock<ILogger<GetManagedAccountHandler>> _mockLogger;
        private readonly GetManagedAccountHandler _handler;

        public GetManagedAccountHandlerTests()
        {
            _mockService = new Mock<IManagedAccountDetailsService>();
            _mockLogger = new Mock<ILogger<GetManagedAccountHandler>>();
            _handler = new GetManagedAccountHandler(_mockService.Object, _mockLogger.Object);
        }

        [Fact]
        public void Constructor_NullService_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new GetManagedAccountHandler(null, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_NullLogger_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new GetManagedAccountHandler(_mockService.Object, null));
        }

        [Fact]
        public async Task Handle_ValidQuery_ReturnsSuccessResult()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };
            var expectedAccount = new ManagedAccountDetails
            {
                ManagedAccountID = accountId,
                ManagedAccountName = "Test Managed Account",
                Domicile = "United States",
                CommencementDate = DateTime.UtcNow,
                InvestmentPeriodEndDate = "2029-12-31",
                MaturityDate = "2034-12-31",
                CommitmentOutstanding = "1000000.00",
                CommitmentOutstandingCurrency = "USD",
                BaseCurrency = "USD",
                InvestmentManager = "ABC Investment Management",
                Administrator = "XYZ Administration Services",
                Custodian = "DEF Custody Bank",
                LegalCounsel = "GHI Legal Services",
                LEI = "12345678901234567890",
                InvestmentSummary = "Investment in technology sector companies",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync(expectedAccount);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Account);
            Assert.Null(result.ErrorMessage);
            Assert.Equal(accountId, result.Account.Id);
            Assert.Equal("Test Managed Account", result.Account.Name);
            Assert.Equal("United States", result.Account.Domicile);

            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_AccountNotFound_ReturnsFailureResult()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync((ManagedAccountDetails?)null);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Account);
            Assert.NotNull(result.ErrorMessage);
            Assert.Contains(accountId.ToString(), result.ErrorMessage);
            Assert.Contains("not found", result.ErrorMessage);

            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_ServiceThrowsException_ReturnsFailureResult()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };
            var exceptionMessage = "Database connection failed";

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Account);
            Assert.NotNull(result.ErrorMessage);
            Assert.Contains(exceptionMessage, result.ErrorMessage);

            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_ValidQuery_CallsServiceOnce()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };
            var account = new ManagedAccountDetails
            {
                ManagedAccountID = accountId,
                ManagedAccountName = "Test Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync(account);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_ValidQuery_MapsPropertiesCorrectly()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };
            var createdDate = DateTime.UtcNow;
            var commencementDate = DateTime.UtcNow.AddDays(-30);

            var expectedAccount = new ManagedAccountDetails
            {
                ManagedAccountID = accountId,
                ManagedAccountName = "Detailed Test Account",
                Domicile = "United Kingdom",
                CommencementDate = commencementDate,
                InvestmentPeriodEndDate = "2030-06-30",
                MaturityDate = "2035-06-30",
                CommitmentOutstanding = "2500000.00",
                CommitmentOutstandingCurrency = "GBP",
                BaseCurrency = "GBP",
                InvestmentManager = "UK Investment Partners",
                Administrator = "London Admin Services",
                Custodian = "British Custody Bank",
                LegalCounsel = "London Legal LLP",
                LEI = "98765432109876543210",
                InvestmentSummary = "Focus on renewable energy investments",
                CreatedBy = 2,
                CreatedOn = createdDate,
                IsActive = true,
                IsDeleted = false
            };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync(expectedAccount);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Account);

            // Verify all mapped properties
            Assert.Equal(accountId, result.Account.Id);
            Assert.Equal("Detailed Test Account", result.Account.Name);
            Assert.Equal("United Kingdom", result.Account.Domicile);
            Assert.Equal(commencementDate, result.Account.CommencementDate);
            Assert.Equal("2030-06-30", result.Account.InvestmentPeriodEndDate);
            Assert.Equal("2035-06-30", result.Account.MaturityDate);
            Assert.Equal("2500000.00", result.Account.CommitmentOutstanding);
            Assert.Equal("GBP", result.Account.CommitmentOutstandingCurrency);
            Assert.Equal("GBP", result.Account.BaseCurrency);
            Assert.Equal("UK Investment Partners", result.Account.InvestmentManager);
            Assert.Equal("London Admin Services", result.Account.Administrator);
            Assert.Equal("British Custody Bank", result.Account.Custodian);
            Assert.Equal("London Legal LLP", result.Account.LegalCounsel);
            Assert.Equal("98765432109876543210", result.Account.LEI);
            Assert.Equal("Focus on renewable energy investments", result.Account.InvestmentSummary);
            Assert.Equal(createdDate, result.Account.CreatedOn);
        }

        [Fact]
        public async Task Handle_CancellationRequested_ReturnsFailureResult()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ThrowsAsync(new OperationCanceledException("Operation was cancelled"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Account);
            Assert.NotNull(result.ErrorMessage);
            Assert.Contains("Operation was cancelled", result.ErrorMessage);

            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_EmptyGuid_ReturnsFailureResult()
        {
            // Arrange
            var query = new GetManagedAccountQuery { Id = Guid.Empty };

            _mockService.Setup(x => x.GetByIdAsync(Guid.Empty))
                .ReturnsAsync((ManagedAccountDetails?)null);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Account);
            Assert.NotNull(result.ErrorMessage);
            Assert.Contains("not found", result.ErrorMessage);

            _mockService.Verify(x => x.GetByIdAsync(Guid.Empty), Times.Once);
        }

        [Fact]
        public async Task Handle_ServiceReturnsNull_LogsWarning()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync((ManagedAccountDetails?)null);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);

            // Verify that the service was called
            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }

        [Fact]
        public async Task Handle_SuccessfulRetrieval_LogsInformation()
        {
            // Arrange
            var accountId = Guid.NewGuid();
            var query = new GetManagedAccountQuery { Id = accountId };
            var account = new ManagedAccountDetails
            {
                ManagedAccountID = accountId,
                ManagedAccountName = "Test Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _mockService.Setup(x => x.GetByIdAsync(accountId))
                .ReturnsAsync(account);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Account);

            // Verify that the service was called
            _mockService.Verify(x => x.GetByIdAsync(accountId), Times.Once);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using ManagedAccounts.Handlers.Queries;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Interfaces;
using DataAccessLayer.ManagedAccounts;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.UnitTest
{
    public class GetAllManagedAccountsHandlerTests
    {
        private readonly Mock<IManagedAccountDetailsService> _mockService;
        private readonly Mock<ILogger<GetAllManagedAccountsHandler>> _mockLogger;
        private readonly GetAllManagedAccountsHandler _handler;

        public GetAllManagedAccountsHandlerTests()
        {
            _mockService = new Mock<IManagedAccountDetailsService>();
            _mockLogger = new Mock<ILogger<GetAllManagedAccountsHandler>>();
            _handler = new GetAllManagedAccountsHandler(_mockService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidQuery_ReturnsSuccessResult()
        {
            // Arrange
            var query = new GetAllManagedAccountsQuery();
            var expectedAccounts = new List<ManagedAccountDetails>
            {
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 1",
                    Domicile = "United States",
                    CreatedBy = 1,
                    CreatedOn = DateTime.UtcNow,
                    IsActive = true,
                    IsDeleted = false
                },
                new ManagedAccountDetails
                {
                    ManagedAccountID = Guid.NewGuid(),
                    ManagedAccountName = "Account 2",
                    Domicile = "United Kingdom",
                    CreatedBy = 1,
                    CreatedOn = DateTime.UtcNow,
                    IsActive = true,
                    IsDeleted = false
                }
            };

            _mockService.Setup(x => x.GetAllAsync())
                .ReturnsAsync(expectedAccounts);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Accounts);
            Assert.Equal(2, result.Accounts.Count());
            Assert.Null(result.ErrorMessage);

            // Verify DTOs contain expected data
            var accountsList = result.Accounts.ToList();
            Assert.Equal("Account 1", accountsList[0].Name);
            Assert.Equal("Account 2", accountsList[1].Name);

            _mockService.Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_EmptyResult_ReturnsSuccessWithEmptyList()
        {
            // Arrange
            var query = new GetAllManagedAccountsQuery();
            var emptyAccounts = new List<ManagedAccountDetails>();

            _mockService.Setup(x => x.GetAllAsync())
                .ReturnsAsync(emptyAccounts);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Accounts);
            Assert.Empty(result.Accounts);
            Assert.Null(result.ErrorMessage);

            _mockService.Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ServiceThrowsException_ReturnsFailureResult()
        {
            // Arrange
            var query = new GetAllManagedAccountsQuery();
            var exceptionMessage = "Database connection failed";

            _mockService.Setup(x => x.GetAllAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Accounts);
            Assert.Contains(exceptionMessage, result.ErrorMessage);

            _mockService.Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ValidQuery_CallsServiceOnce()
        {
            // Arrange
            var query = new GetAllManagedAccountsQuery();
            var accounts = new List<ManagedAccountDetails>();

            _mockService.Setup(x => x.GetAllAsync())
                .ReturnsAsync(accounts);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _mockService.Verify(x => x.GetAllAsync(), Times.Once);
        }
    }
}

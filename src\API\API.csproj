﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Controllers\Workflow\Helper\" />
    <Folder Include="MyDashboards\" />
    <Folder Include="Resources\ImportOrExport\" />
  </ItemGroup>


  <ItemGroup>
	  <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.301" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.304.16" />
    <PackageReference Include="Castle.Core" Version="5.1.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.28.0" />
	<PackageReference Include="dbup" Version="5.0.40" />
	<PackageReference Include="Elastic.Apm.SerilogEnricher" Version="8.11.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.1.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.1" />
    <PackageReference Include="MimeTypes" Version="2.5.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Reveal.Sdk.AspNetCore" Version="1.7.5" />
    <PackageReference Include="Reveal.Sdk.Data" Version="1.7.5" />
    <PackageReference Include="PdfPig" Version="0.1.10" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.Composition.TypedParts" Version="8.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.7" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Text.Encodings.Web" Version="8.0.0" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
	<PackageReference Include="Serilog.Formatting.Elasticsearch" Version="10.0.0" />
	<PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
    <PackageReference Include="Serilog.Filters.Expressions" Version="2.1.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
	<PackageReference Include="Azure.Identity" Version="1.12.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\CLO\CLO.csproj" />
    <ProjectReference Include="..\Account\Account.csproj" />
    <ProjectReference Include="..\Audit\Audit.csproj" />
    <ProjectReference Include="..\BackGroundJob\BackGroundJob.csproj" />
    <ProjectReference Include="..\CashFlow\CashFlow.csproj" />
    <ProjectReference Include="..\Common\Shared.csproj" />
    <ProjectReference Include="..\Contract\Contract.csproj" />
    <ProjectReference Include="..\Currency\CurrencyRates.csproj" />
    <ProjectReference Include="..\DapperRepository\DapperRepository.csproj" />
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
    <ProjectReference Include="..\DataAnalytic\DataAnalytic.csproj" />
    <ProjectReference Include="..\Deal\Deal.csproj" />
    <ProjectReference Include="..\DocumentCollection\DocumentCollection.csproj" />
    <ProjectReference Include="..\EmailConfiguration\EmailConfiguration.csproj" />
    <ProjectReference Include="..\Financials\Financials.csproj" />
    <ProjectReference Include="..\Firm\Firm.csproj" />
    <ProjectReference Include="..\FormulaCalculator\FormulaCalculator.csproj" />
    <ProjectReference Include="..\Fund\Fund.csproj" />
    <ProjectReference Include="..\Group\Group.csproj" />
    <ProjectReference Include="..\Master\Master.csproj" />
    <ProjectReference Include="..\Notification\Notification.csproj" />
    <ProjectReference Include="..\Pipeline\Pipeline.csproj" />
    <ProjectReference Include="..\PortfolioCompany\PortfolioCompany.csproj" />
    <ProjectReference Include="..\Report\Report.csproj" />
    <ProjectReference Include="..\Repository\Repository.csproj" />
    <ProjectReference Include="..\TokenManager.Service\TokenManager.Service.csproj" />
    <ProjectReference Include="..\Utility\Utility.csproj" />
    <ProjectReference Include="..\DataCollection\DataCollection.csproj" />
    <ProjectReference Include="..\Valuation\Valuation.csproj" />
    <ProjectReference Include="..\Workflow\Workflow.csproj" />
    <ProjectReference Include="..\ManagedAccounts\ManagedAccounts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DeploymentSqlScript\4Index.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\5Functions - Copy %282%29.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\5Functions.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\6Views.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\7StoreProcedures.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\Valuation.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\1Tables.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\2AlterTables.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\3InsertOrUpdateTables.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
	  <None Update="DeploymentSqlScript\4Index.sql">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
    <None Update="DeploymentSqlScript\5.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="BackupSQLScripts\Release1.5.sql">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Update="DeploymentSqlScript\Backup\1.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Templates\Dashboard.rdash">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="lib\*">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Templates\*.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>

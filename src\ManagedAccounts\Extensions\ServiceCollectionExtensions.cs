using Microsoft.Extensions.DependencyInjection;
using MediatR;
using FluentValidation;
using FluentValidation.AspNetCore;
using AutoMapper;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Services;
using System.Diagnostics.CodeAnalysis;

namespace ManagedAccounts.Extensions
{
    [ExcludeFromCodeCoverage]
    /// <summary>
    /// Extension methods for registering ManagedAccounts services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Registers all ManagedAccounts services and handlers
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddManagedAccounts(this IServiceCollection services)
        {
            // Register MediatR
            services.AddMediatR(typeof(ServiceCollectionExtensions).Assembly);

            // Register AutoMapper
            services.AddAutoMapper(typeof(ServiceCollectionExtensions).Assembly);

            // Register FluentValidation
            services.AddFluentValidationAutoValidation();
            services.AddValidatorsFromAssembly(typeof(ServiceCollectionExtensions).Assembly);

            // Register services
            services.AddScoped<IManagedAccountDetailsService, ManagedAccountDetailsService>();

            // Register handlers
            services.AddTransient<IRequestHandler<Models.Commands.CreateManagedAccountCommand, Models.Results.CreateManagedAccountResult>, Handlers.Commands.CreateManagedAccountHandler>();
            services.AddTransient<IRequestHandler<Models.Queries.GetManagedAccountQuery, Models.Results.GetManagedAccountResult>, Handlers.Queries.GetManagedAccountHandler>();
            services.AddTransient<IRequestHandler<Models.Queries.GetAllManagedAccountsQuery, Models.Results.GetAllManagedAccountsResult>, Handlers.Queries.GetAllManagedAccountsHandler>();

            return services;
        }
    }
}

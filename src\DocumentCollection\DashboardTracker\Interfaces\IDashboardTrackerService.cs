﻿using CLO.CQRS.Results;
using Contract.PortfolioCompany;
using Contract.Utility;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Models;

namespace DocumentCollection.DashboardTracker.Interfaces
{
    public interface IDashboardTrackerService
    {
        Task<IEnumerable<PortfolioCompanyQueryModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter);
        Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto);
        Task<List<ColumnsDto>> GetDashboardColumnsAsync();        
        Task<bool> SaveTrackerDropdownValuesAsync(TrackerDropdownValueDto dto);
        Task<List<DashboardTrackerConfigDto>> GetAllTrackerConfigsAsync();
        Task<TableDataResultDto> GetDashboardTableDataAsync(int userId, PaginationFilter filter);
        Task<bool> SaveDashboardCellValuesAsync(SaveDashboardCellValuesDto dto, int userId);
        Task<bool> DeleteDashboardTrackerConfigAsync(int id);
        Task<List<ColumnAlias>> GetAvailableCustomFields();
        Task<bool> DeleteDashboardTrackerColumnsAsync(List<ColumnsDto> columns);
        Task<TableDataResultDto> GetDeletedColumnsAsync(int userId, PaginationFilter filter);
    }
}

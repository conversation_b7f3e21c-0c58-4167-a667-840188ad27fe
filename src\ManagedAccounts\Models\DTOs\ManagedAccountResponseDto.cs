using System;

namespace ManagedAccounts.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Managed Account API responses
    /// </summary>
    public class ManagedAccountResponseDto
    {
        /// <summary>
        /// Unique identifier for the managed account
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Name of the managed account
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Legal domicile of the managed account
        /// </summary>
        public string? Domicile { get; set; }

        /// <summary>
        /// Date when the managed account commenced
        /// </summary>
        public DateTime? CommencementDate { get; set; }

        /// <summary>
        /// End date of the investment period
        /// </summary>
        public string? InvestmentPeriodEndDate { get; set; }

        /// <summary>
        /// Maturity date of the managed account
        /// </summary>
        public string? MaturityDate { get; set; }

        /// <summary>
        /// Outstanding commitment amount
        /// </summary>
        public string? CommitmentOutstanding { get; set; }

        /// <summary>
        /// Currency for the commitment outstanding amount
        /// </summary>
        public string? CommitmentOutstandingCurrency { get; set; }

        /// <summary>
        /// Base currency of the managed account
        /// </summary>
        public string? BaseCurrency { get; set; }

        /// <summary>
        /// Investment manager for the managed account
        /// </summary>
        public string? InvestmentManager { get; set; }

        /// <summary>
        /// Administrator of the managed account
        /// </summary>
        public string? Administrator { get; set; }

        /// <summary>
        /// Custodian of the managed account
        /// </summary>
        public string? Custodian { get; set; }

        /// <summary>
        /// Legal counsel for the managed account
        /// </summary>
        public string? LegalCounsel { get; set; }

        /// <summary>
        /// Legal Entity Identifier
        /// </summary>
        public string? LEI { get; set; }

        /// <summary>
        /// Investment summary details
        /// </summary>
        public string? InvestmentSummary { get; set; }

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedOn { get; set; }
    }
}

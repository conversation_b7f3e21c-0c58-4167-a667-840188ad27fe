GO
IF NOT EXISTS(SELECT * FROM [dbo].[M_KpiModules] WHERE [Name] = 'CustomTable5')
BEGIN
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable5',GETDATE(),3,0,'Custom Table5','Custom Table5',1,0,31,0,'CustomTable5'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable6',GETDATE(),3,0,'Custom Table6','Custom Table6',1,0,32,0,'CustomTable6'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable7',GETDATE(),3,0,'Custom Table7','Custom Table7',1,0,33,0,'CustomTable7'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable8',GETDATE(),3,0,'Custom Table8','Custom Table8',1,0,34,0,'CustomTable8'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'CustomTable9',GETDATE(),3,0,'Custom Table9','Custom Table9',1,0,35,0,'CustomTable9'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI11',GETDATE(),3,0,'Other KPI11','Other KPI11',1,0,36,0,'OtherKPI11'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI12',GETDATE(),3,0,'Other KPI12','Other KPI12',1,0,37,0,'OtherKPI12'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI13',GETDATE(),3,0,'Other KPI13','Other KPI13',1,0,38,0,'OtherKPI13'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI14',GETDATE(),3,0,'Other KPI14','Other KPI14',1,0,39,0,'OtherKPI14'		   
)
INSERT INTO [dbo].[M_KpiModules]([Name],[CreatedOn],[CreatedBy],[IsDeleted],[AliasName],[TabName],[IsActive],[IsFinacials],[OrderBy],[IsBulkUpload],[PageConfigFieldName])     
VALUES
(		   
'OtherKPI15',GETDATE(),3,0,'Other KPI15','Other KPI15',1,0,40,0,'OtherKPI15'		   
)
END
GO
IF NOT EXISTS(SELECT * FROM [dbo].[M_SubPageFields] WHERE [Name] = 'CustomTable5')
BEGIN
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable5','Custom Table5',2,1,0,GETDATE(),3,11,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable6','Custom Table6',2,1,0,GETDATE(),3,12,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable7','Custom Table7',2,1,0,GETDATE(),3,13,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable8','Custom Table8',2,1,0,GETDATE(),3,14,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'CustomTable9','Custom Table9',2,1,0,GETDATE(),3,15,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI11','Other KPI11',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,11,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI12','Other KPI12',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,12,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI13','Other KPI13',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,13,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI14','Other KPI14',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,14,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[M_SubPageFields]([Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[IsCustom],[isMandatory],[DataTypeId],[IsListData],[ShowOnList],[IsChart])     
VALUES
(
'OtherKPI15','Other KPI15',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,15,0,0,0,0,0,0		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable5'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable6'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable7'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable8'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual','Actual',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget','Budget',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast','Forecast',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC','IC',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC2','IC2',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC3','IC3',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC4','IC4',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'IC5','IC5',2,1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual LTM','Actual LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget LTM','Budget LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast LTM','Forecast LTM',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Actual YTD','Actual YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Budget YTD','Budget YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=2 and name='CustomTable9'),'Forecast YTD','Forecast YTD',2,1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI11'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI12'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI13'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI14'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual','Actual',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget','Budget',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast','Forecast',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC','IC',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC2','IC2',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC3','IC3',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC4','IC4',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'IC5','IC5',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly,Annual','Monthly,Quarterly,Annual'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual LTM','Actual LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget LTM','Budget LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast LTM','Forecast LTM',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Actual YTD','Actual YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Budget YTD','Budget YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
INSERT INTO [dbo].[MSubSectionFields]([FieldID],[Name],[AliasName],[SubPageID],[isActive],[isDeleted],[CreatedOn],[CreatedBy],[SequenceNo],[Options],[ChartValue])     
VALUES(		   
(Select top 1 FieldID from M_SubPageFields  where SubPageID=(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs') and name='OtherKPI15'),'Forecast YTD','Forecast YTD',(Select top 1 SubPageID from M_SubPageDetails  where PageID=1 and name='Other KPIs'),1,0,GETDATE(),3,1,'Monthly,Quarterly','Monthly,Quarterly'		   
)
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Custom Table5')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (125, N'Custom Table5', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable5')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (126, N'Custom Table6', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable6')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (127, N'Custom Table7', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable7')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (128, N'Custom Table8', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable8')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (129, N'Custom Table9', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'CustomTable9')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (130, N'Other KPI11', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI11')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (131, N'Other KPI12', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI12')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (132, N'Other KPI13', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI13')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (133, N'Other KPI14', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI14')
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (134, N'Other KPI15', 14, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'OtherKPI15')
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table5'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table6'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table7'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table8'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Custom Table9'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI11'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI12'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI13'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI14'), 5, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 2, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 3, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 4, 0, getdate(), 3, NULL, NULL, NULL)
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other KPI15'), 5, 0, getdate(), 3, NULL, NULL, NULL)
END

GO
IF EXISTS (SELECT 1 FROM [dbo].[DashboardTrackerConfig] WHERE MapTo IS NOT NULL AND (MaptoType IS NULL OR MaptoType = 0))
BEGIN
	UPDATE [dbo].[DashboardTrackerConfig]
	SET MaptoType = 1
	WHERE MapTo IS NOT NULL AND (MaptoType IS NULL OR MaptoType = 0);
END
GO
IF NOT EXISTS ( SELECT 1 FROM FinancialValueTypes WHERE Name = 'Since Inception' AND IsDeleted = 0)
BEGIN
    INSERT INTO FinancialValueTypes (Name, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, Alias)
    VALUES ('Since Inception', GETDATE(), 3, NULL, NULL, 0, NULL)
END
GO

----------------------------------------- BFB-11438-managed-accounts -------------------------

IF NOT EXISTS(Select * from M_Features Where Feature='Managed Accounts' )
BEGIN
INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath])
VALUES (64, N'Managed Accounts', NULL, NULL, 1, 0, GETDATE(), 3, NULL, NULL, NULL, 39, N'Managed Accounts', N'/managed-accounts')
END
GO

IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 64				 
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 1, 1, GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 2, 1, GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (64, 3, 1, GETDATE(), 1, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (
	SELECT 1 FROM DashboardTrackerConfig
	WHERE [Name]='SerialNo' AND [FieldType]=1 AND [DataType]=1
)
BEGIN
INSERT INTO [dbo].[DashboardTrackerConfig] (
   [FieldType], [DataType], [Name], [FrequencyType], [StartPeriod], [EndPeriod], [IsPrefix], [TimeSeriesDateFormat], [MapTo], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [MaptoType], [DeletedColumns]
)
VALUES (
    1, 1, 'SerialNo', 1, NULL, NULL, NULL, NULL, NULL, 1, 0, GETDATE(), 1135, NULL, NULL, 0, NULL
)
END
GO
GO

IF NOT EXISTS (SELECT * FROM [dbo].[M_PageDetails] WHERE [Name] = 'Managed Accounts') 
BEGIN 
INSERT INTO [dbo].[M_PageDetails]([Name], [AliasName], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'Managed Accounts', 'Managed Accounts', NULL, 'Page for managing account information and facts', 1, 0, GETDATE(), 1, NULL, NULL, NULL, (SELECT ISNULL(MAX([SequenceNo]), 0) + 1 FROM [dbo].[M_PageDetails]), '/managed-accounts', 0 ) 
END

GO
-- Step 2: Insert Managed Account Facts sub-page into M_SubPageDetails if not exists 
IF NOT EXISTS (SELECT * FROM [dbo].[M_SubPageDetails] WHERE [Name] = 'Managed Account Facts' AND [PageID] = (SELECT [PageID] FROM [dbo].[M_PageDetails] WHERE [Name] = 'Managed Accounts')) 
BEGIN 
INSERT INTO [dbo].[M_SubPageDetails]( [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported] ) 
VALUES ( 'Managed Account Facts', 'Managed Account Facts', (SELECT [PageID] FROM [dbo].[M_PageDetails] WHERE [Name] = 'Managed Accounts'), NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, '/managed-accounts/facts', 0, 1 ) 

DECLARE @SubPageID INT = (SELECT [SubPageID] FROM [dbo].[M_SubPageDetails] WHERE [Name] = 'Managed Account Facts' AND [PageID] = (SELECT [PageID] FROM [dbo].[M_PageDetails] WHERE [Name] = 'Managed Accounts')) 

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'Domicile', 'Domicile', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0 ) 

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'CommencementDate', 'Commencement Date', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'InvestmentPeriodEndDate', 'Investment Period End Date', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 3, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'MaturityDate', 'Maturity Date', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 4, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'CommitmentOutstanding', 'Commitment Outstanding', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 5, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] )
VALUES ( 'BaseCurrency', 'Base Currency', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'InvestmentManager', 'Investment Manager', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0 ) 

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'Administrator', 'Administrator', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'Custodian', 'Custodian', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 9, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] ) 
VALUES ( 'LegalCounsel', 'Legal Counsel', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 10, NULL, 0 )

INSERT INTO [dbo].[M_SubPageFields]( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom] )
VALUES ( 'LEI', 'LEI', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 11, NULL, 0 )
END
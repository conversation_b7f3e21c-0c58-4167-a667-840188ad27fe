using System;

namespace ManagedAccounts.Models.DTOs
{
    /// <summary>
    /// Summary Data Transfer Object for Managed Account list responses
    /// Contains only essential information for list views
    /// </summary>
    public class ManagedAccountSummaryDto
    {
        /// <summary>
        /// Unique identifier for the managed account
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Name of the managed account
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Legal domicile of the managed account
        /// </summary>
        public string? Domicile { get; set; }

        /// <summary>
        /// Date when the managed account commenced
        /// </summary>
        public DateTime? CommencementDate { get; set; }

        /// <summary>
        /// Base currency of the managed account
        /// </summary>
        public string? BaseCurrency { get; set; }

        /// <summary>
        /// Investment manager for the managed account
        /// </summary>
        public string? InvestmentManager { get; set; }

        /// <summary>
        /// Date when the record was created
        /// </summary>
        public DateTime CreatedOn { get; set; }
    }
}

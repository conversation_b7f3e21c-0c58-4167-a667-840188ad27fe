using System;
using ManagedAccounts.Models.DTOs;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for getting a managed account
    /// </summary>
    public class GetManagedAccountResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// The managed account details
        /// </summary>
        public ManagedAccountResponseDto? Account { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="account">The managed account details</param>
        /// <returns>Success result</returns>
        public static GetManagedAccountResult Success(ManagedAccountResponseDto account)
        {
            return new GetManagedAccountResult
            {
                IsSuccess = true,
                Account = account
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <returns>Failure result</returns>
        public static GetManagedAccountResult Failure(string errorMessage)
        {
            return new GetManagedAccountResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}

﻿using Contract.Funds;
using Contract.PortfolioCompany;
using Contract.Repository;
using Contract.Utility;
using DataAccessLayer.Models.DashboardTracker;
using DataAccessLayer.Models.Tracker;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Helpers;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using PortfolioCompany.Helper;
using Workflow;

namespace DocumentCollection.DashboardTracker.Services
{
    public class DashboardTrackerService : IDashboardTrackerService
    {
        private readonly IWorkflowPCService _workflowPCService;
        private readonly IFileService _fileService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly int DEFAULT_PAGE_NUMBER = 1;
        private readonly int DEFAULT_PAGE_SIZE = 100;

        public DashboardTrackerService(IWorkflowPCService workflowPCService, IFileService fileService, IUnitOfWork unitOfWork)
        {
            _workflowPCService = workflowPCService;
            _fileService = fileService;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PortfolioCompanyQueryModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter)
        {
            var workflowResult = await _workflowPCService.GetPortfolioCompanies(portfolioCompanyFilter);

            if (workflowResult == null || workflowResult.PortfolioCompanyQueryListModel == null || workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Count == 0)
            {
                return new List<PortfolioCompanyQueryModel>();
            }
            return workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList;
        }

        public async Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto)
        {
            var entity = new DashboardTrackerConfig
            {
                FieldType = dto.FieldType,
                DataType = dto.DataType,
                Name = dto.Name,
                FrequencyType = dto.FrequencyType,
                StartPeriod = dto.StartPeriod,
                EndPeriod = dto.EndPeriod,
                IsPrefix = dto.IsPrefix,
                TimeSeriesDateFormat = dto.TimeSeriesDateFormat,
                MapTo = dto.MapTo == 0 || dto.MapTo == null ? null : (MapWith?)dto.MapTo,
                MaptoType = dto.MapTo == 0 || dto.MapTo == null ? null : (MapToType?)dto.MapToType,
                IsActive = dto.IsActive,
                IsDeleted = dto.IsDeleted,
            };

            if (dto.ID.HasValue && dto.ID.Value > 0)
            {
                // Update existing record
                entity.ModifiedOn = dto.ModifiedOn;
                entity.ModifiedBy = dto.ModifiedBy;
                entity.ID = dto.ID.Value;
                _unitOfWork.DashboardTrackerConfigRepository.Update(entity);
            }
            else
            {
                // Insert new record
                entity.CreatedBy = dto.CreatedBy ?? 0;
                entity.CreatedOn = dto.CreatedOn ?? DateTime.UtcNow;
                _unitOfWork.DashboardTrackerConfigRepository.Insert(entity);
            }
            await _unitOfWork.SaveAsync();
            return entity.ID;
        }

        /// <summary>
        /// Gets all active dashboard tracker configurations and generates corresponding ColumnsDto array
        /// </summary>
        /// <returns>List of ColumnsDto objects</returns>
        public async Task<List<ColumnsDto>> GetDashboardColumnsAsync()
        {
            var configs = await _unitOfWork.DashboardTrackerConfigRepository.FindAllAsync(x => x.IsActive && !x.IsDeleted);
            return DashboardTrackerHelper.GenerateColumnsDtoArray(configs);
        }

        public async Task<TableDataResultDto> GetDashboardTableDataAsync(int userId, PaginationFilter filter)
        {
            try
            {
                var columnsDto = await GetDashboardColumnsAsync();
                return await GenerateDashboardTableDataCommonAsync(userId, filter, columnsDto, true, 0);
            }
            catch (Exception)
            {
                return new TableDataResultDto
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = new List<ColumnsDto>(),
                    TotalRecords = 0
                };
            }
        }

        public async Task<bool> SaveTrackerDropdownValuesAsync(TrackerDropdownValueDto dto)
        {
            if (dto == null || dto.DropdownValueWithType == null || dto.DropdownValueWithType.Count == 0)
                return false;

            var now = DateTime.UtcNow;

            foreach (var item in dto.DropdownValueWithType)
            {
                if (item?.DropdownValues!.Count == 0)
                {
                    return false;
                }

                if (item.DropdownValues != null && item.DropdownValues.Count > 0)
                {
                    var entities = item.DropdownValues.Select(val => new TrackerDropdownValue
                    {
                        TackerFieldConfigId = dto.TrackerFieldId,
                        DropdownType = item.DropdownType,
                        DropdownValue = val,
                        CreatedOn = now
                    }).ToList();

                    await _unitOfWork.TrackerDropdownValueRepository.AddBulkAsyn(entities);

                    await _unitOfWork.SaveAsync();
                }
            }

            return true;
        }

        public async Task<List<DashboardTrackerConfigDto>> GetAllTrackerConfigsAsync()
        {
            var configs = await _unitOfWork.DashboardTrackerConfigRepository.FindAllAsync(x => !x.IsDeleted && x.Name != DashboardTrackerHelper.SerialNo);
            var configDtos = configs.Select(config => new DashboardTrackerConfigDto
            {
                ID = config.ID,
                FieldType = config.FieldType,
                FieldTypeName = Enum.GetName(typeof(FieldTypeEnum), config.FieldType),
                DataType = config.DataType,
                DataTypeName = Enum.GetName(typeof(DataTypeEnum), config.DataType),
                Name = config.Name,
                StartPeriod = config.StartPeriod,
                MapTo = config.MapTo.HasValue ? (int)config.MapTo.Value : 0,
                CreatedOn = config.CreatedOn,
            }).ToList();

            // Get all dropdown config IDs (non-nullable)
            var dropdownConfigIds = configDtos
                .Where(x => x.DataType == (int)DataTypeEnum.Dropdown && x.ID.HasValue)
                .Select(x => x.ID.Value)
                .ToList();

            if (dropdownConfigIds.Count > 0)
            {
                // Fetch all dropdown values for these config IDs in a single query
                var allDropdowns = await _unitOfWork.TrackerDropdownValueRepository
                    .FindAllAsync(x => dropdownConfigIds.Contains(x.TackerFieldConfigId));

                // Group dropdown values by config ID
                var dropdownsByConfig = allDropdowns
                    .GroupBy(d => d.TackerFieldConfigId)
                    .ToDictionary(g => g.Key, g => g.Select(d => d.DropdownValue).ToList());

                // Assign dropdown lists to each DTO
                foreach (var dto in configDtos.Where(x => x.DataType == (int)DataTypeEnum.Dropdown && x.ID.HasValue))
                {
                    if (dropdownsByConfig.TryGetValue(dto.ID.Value, out var dropdownList))
                        dto.DropdownList = dropdownList;
                    else
                        dto.DropdownList = new List<string>();
                }
            }

            return configDtos;
        }

        public async Task<bool> SaveDashboardCellValuesAsync(SaveDashboardCellValuesDto dto, int userId)
        {
            try
            {
                var currentDateTime = DateTime.UtcNow;
                var entities = new List<DashboardTrackerCellValue>();

                foreach (var cellValue in dto.CellValues)
                {
                    // Check if a record already exists for this combination
                    var existingEntity = await _unitOfWork.DashboardTrackerCellValueRepository
                        .FindFirstAsync(x => x.PortfolioCompanyId == cellValue.PortfolioCompanyId &&
                                           x.FundId == cellValue.FundId &&
                                           x.ColumnId == cellValue.ColumnId &&
                                           x.TimeSeriesID == cellValue.TimeSeriesID &&
                                           !x.IsDeleted);

                    if (existingEntity != null)
                    {
                        // Update existing record
                        existingEntity.CellValue = cellValue.CellValue;
                        existingEntity.ModifiedBy = userId;
                        existingEntity.ModifiedOn = currentDateTime;
                        _unitOfWork.DashboardTrackerCellValueRepository.Update(existingEntity);
                    }
                    else
                    {
                        // Create new record
                        var newEntity = new DashboardTrackerCellValue
                        {
                            PortfolioCompanyId = cellValue.PortfolioCompanyId,
                            FundId = cellValue.FundId,
                            ColumnId = cellValue.ColumnId,
                            TimeSeriesID = cellValue.TimeSeriesID,
                            CellValue = cellValue.CellValue,
                            CreatedBy = userId,
                            CreatedOn = currentDateTime,
                            IsActive = true,
                            IsDeleted = false
                        };
                        _unitOfWork.DashboardTrackerCellValueRepository.Insert(newEntity);
                    }
                }

                await _unitOfWork.SaveAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteDashboardTrackerConfigAsync(int id)
        {
            var dropdownValues = await _unitOfWork.TrackerDropdownValueRepository
                .FindAllAsync(x => x.TackerFieldConfigId == id);

            if (dropdownValues != null && dropdownValues.Count > 0)
            {
                foreach (var value in dropdownValues)
                {
                    _unitOfWork.TrackerDropdownValueRepository.Delete(value);
                }
                await _unitOfWork.SaveAsync();
            }
            var entity = await _unitOfWork.DashboardTrackerConfigRepository.FindFirstAsync(x => x.ID == id);
            if (entity == null)
                return false;

            _unitOfWork.DashboardTrackerConfigRepository.Delete(entity);
            await _unitOfWork.SaveAsync();
            return true;
        }

        public async Task<List<ColumnAlias>> GetAvailableCustomFields()
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(
                x => !x.IsDeleted
                    && x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation
                    && x.IsCustom == true
                    && x.DataTypeId.HasValue
                    && x.DataTypeId.Value != 0
                    && x.DataTypeId.Value != (int)PageSubFieldsDatatTypes.List
            );
            return configurations.Select(i => new ColumnAlias() { Value = i.FieldID.ToString(), DisplayText = i.AliasName }).ToList();
        }

        public async Task<bool> DeleteDashboardTrackerColumnsAsync(List<ColumnsDto> columns)
        {
            try
            {
                var timeSeriesDashBoardColumns = columns.Where(x => x.IsTimeSeries && x.FieldType == (int)FieldTypeEnum.TimeSeries).ToList();
                if (timeSeriesDashBoardColumns.Count > 0 && timeSeriesDashBoardColumns != null)
                {
                    await DeleteTimeSeriesCoulmns(columns);
                }

                var nonTimeSeriesDashBoardColumns = columns.Where(x => x.FieldType == (int)FieldTypeEnum.Data).ToList();
                if (nonTimeSeriesDashBoardColumns.Count > 0 && nonTimeSeriesDashBoardColumns != null)
                {
                    foreach (var item in nonTimeSeriesDashBoardColumns)
                    {
                        var dashBordTrackerColumn = await _unitOfWork.DashboardTrackerConfigRepository.FindFirstAsync(x => x.ID == item.ID);
                        if (dashBordTrackerColumn != null)
                        {
                            var existingDeletedColumns = !string.IsNullOrEmpty(dashBordTrackerColumn.DeletedColumns) 
                                ? dashBordTrackerColumn.DeletedColumns.Split(',').ToList() 
                                : new List<string>();
                            if (!existingDeletedColumns.Contains(item.Name, StringComparer.OrdinalIgnoreCase))
                            {
                                var allDeletedColumns = existingDeletedColumns.Concat(new List<string> { item.Name }).ToList();
                                dashBordTrackerColumn.DeletedColumns = string.Join(",", allDeletedColumns);
                                _unitOfWork.DashboardTrackerConfigRepository.Update(dashBordTrackerColumn);
                            }
                        }
                    }
                }

                var affectedRows = await _unitOfWork.SaveAsync();
                return affectedRows > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<TableDataResultDto> GetDeletedColumnsAsync(int userId, PaginationFilter filter)
        {
            try
            {
                // Get all dashboard tracker configs that have deleted columns
                var dashboardTrackerConfigs = await _unitOfWork.DashboardTrackerConfigRepository
                    .FindAllAsync(x => x.IsActive && !x.IsDeleted && !string.IsNullOrEmpty(x.DeletedColumns));

                if (dashboardTrackerConfigs == null || dashboardTrackerConfigs.Count == 0)
                {
                    return new TableDataResultDto
                    {
                        Success = true,
                        Data = new List<Dictionary<string, object>>(),
                        Columns = new List<ColumnsDto>(),
                        TotalRecords = 0
                    };
                }

                List<ColumnsDto> deletedColumnsList = GetDashboardTrackerDeletedColumns(dashboardTrackerConfigs);
                
                if (!deletedColumnsList.Any())
                {
                    return new TableDataResultDto
                    {
                        Success = true,
                        Data = new List<Dictionary<string, object>>(),
                        Columns = new List<ColumnsDto>(),
                        TotalRecords = 0
                    };
                }

                // Use the common method with deleted columns, limit to 100 companies, and don't include default columns
                return await GenerateDashboardTableDataCommonAsync(userId, filter, deletedColumnsList, false, 100);
            }
            catch (Exception)
            {
                return new TableDataResultDto
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = new List<ColumnsDto>(),
                    TotalRecords = 0
                };
            }
        }

        // ==================== PRIVATE METHODS ====================

        private async Task<Dictionary<int, string>> Fetchlogosforcompanies(IEnumerable<PortfolioCompanyQueryModel> portfolioCompanies)
        {
            var companiesToFetchLogo = portfolioCompanies.Take(50).ToList();
            Dictionary<int, string> result = new Dictionary<int, string>();
            foreach (var viewModel in companiesToFetchLogo)
            {
                var res = await _unitOfWork.PortfolioCompanyDetailRepository.FindFirstAsync(x => x.PortfolioCompanyId == viewModel.PortfolioCompanyID);
                if (res != null && !result.ContainsKey(viewModel.PortfolioCompanyID))
                {
                    var imgPath = await PortfolioCompanyHelper.GetCompanyLogo(_fileService, viewModel.PortfolioCompanyID, res.ImagePath);
                    result.Add(viewModel.PortfolioCompanyID, imgPath);
                }
            }

            return result;
        }

        /// <summary>
        /// Common method to generate dashboard table data with pagination, company data, and cell values
        /// </summary>
        private async Task<TableDataResultDto> GenerateDashboardTableDataCommonAsync(
            int userId, 
            PaginationFilter filter, 
            List<ColumnsDto> columns,
            bool includeDefaultColumns = true,
            int maxCompanies = 0)
        {
            var companiesAll = await GetPortfolioCompanies(new PortfolioCompanyFilter { CreatedBy = userId });
            
            // Apply company limit if specified
            var companiesToProcess = maxCompanies > 0 ? companiesAll?.Take(maxCompanies) : companiesAll;

            int pageNumber = DEFAULT_PAGE_NUMBER;
            int pageSize = DEFAULT_PAGE_SIZE;
            if (filter != null)
            {
                pageSize = filter.Rows > 0 ? filter.Rows : 100;
                pageNumber = filter.First > 0 ? (filter.First / pageSize) + 1 : 1;
            }
            
            int totalRecords;
            var companies = ApplyPagination(companiesToProcess, pageNumber, pageSize, out totalRecords);
            var imagedata = await Fetchlogosforcompanies(companies);

            // Prepare columns list
            List<ColumnsDto> finalColumns = columns;
            if (includeDefaultColumns)
            {
                finalColumns = AddDefaultColumns();
                finalColumns.AddRange(columns);
            }

            await GetDropdownValuesforColumn(columns);

            var companyIds = companies.Select(c => c.PortfolioCompanyID).ToList();

            // Get stored cell values for dashboard tracker
            var cellValues = await _unitOfWork.DashboardTrackerCellValueRepository
                .FindAllAsync(x => companyIds.Contains(x.PortfolioCompanyId) &&
                                 x.IsActive && !x.IsDeleted);

            // Get custom field values for portfolio companies
            var customFieldValues = await _unitOfWork.PageConfigurationFieldValueRepository
                .FindAllAsync(x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany &&
                                 companyIds.Contains(x.PageFeatureId) &&
                                 x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation);

            var data = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columns, cellValues, customFieldValues);

            return new TableDataResultDto
            {
                Success = true,
                Data = data,
                Columns = finalColumns,
                TotalRecords = totalRecords
            };
        }

        /// <summary>
        /// Paginates a list based on page number and page size.
        /// </summary>
        private static List<T> ApplyPagination<T>(IEnumerable<T> source, int pageNumber, int pageSize, out int totalRecords)
        {
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1) pageSize = 100;
            var list = source.ToList();
            totalRecords = list.Count;
            return list.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        }

        private static List<ColumnsDto> AddDefaultColumns()
        {
            return new List<ColumnsDto>
            {
                new() { Name = DashboardTrackerHelper.FundId, DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = DashboardTrackerHelper.PortfolioCompanyId, DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = DashboardTrackerHelper.FundName, DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = DashboardTrackerHelper.PortfolioCompanyName, DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = DashboardTrackerHelper.LogoColumn, DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false }
            };
        }

        private async Task GetDropdownValuesforColumn(List<ColumnsDto> columnsDto)
        {
            var dropdownValuesDict = new Dictionary<int, List<string>>();
            var dataType4Columns = columnsDto.Where(col => col.DataType == 4).ToList();
            foreach (var col in dataType4Columns)
            {
                // Fetch dropdown values for each DataType 4 column (if needed)
                var dropdownValues = await _unitOfWork.TrackerDropdownValueRepository.FindAllAsync(x => x.TackerFieldConfigId == col.ID);
                if (dropdownValues != null && dropdownValues.Any())
                {
                    col.DropDownValues = dropdownValues.Select(x => new ColumnAlias() { Value = x.TackerDropdownValueId.ToString(), DisplayText = x.DropdownValue, Type = x.DropdownType }).ToList();
                }
            }

        }

        private async Task DeleteTimeSeriesCoulmns(List<ColumnsDto> activeColumns)
        {
            // Get all TimeSeriesID values grouped by ID
            var timeSeriesGroupedByIds = activeColumns
                .Where(x => x.IsTimeSeries && x.FieldType == (int)FieldTypeEnum.TimeSeries && !string.IsNullOrEmpty(x.TimeSeriesID))
                .GroupBy(x => x.ID)
                .ToDictionary(
                   g => g.Key,
                   g => g.Select(x => x.TimeSeriesID).Where(id => !string.IsNullOrEmpty(id)).ToList());

            foreach (var item in timeSeriesGroupedByIds)
            {
                var timeSeries = await _unitOfWork.DashboardTrackerConfigRepository.FindFirstAsync(x => x.ID == item.Key);
                if (timeSeries != null)
                {

                    var existingDeletedColumns = !string.IsNullOrEmpty(timeSeries.DeletedColumns)
                        ? timeSeries.DeletedColumns.Split(',').ToList()
                        : new List<string>();

                    var newDeletedColumns = item.Value.Except(existingDeletedColumns, StringComparer.OrdinalIgnoreCase).ToList();
                    if (newDeletedColumns.Any())
                    {
                        var allDeletedColumns = existingDeletedColumns.Concat(newDeletedColumns).ToList();
                        timeSeries.DeletedColumns = string.Join(",", allDeletedColumns);
                        _unitOfWork.DashboardTrackerConfigRepository.Update(timeSeries);
                    }
                }
            }
        }

        private static List<ColumnsDto> GetDashboardTrackerDeletedColumns(List<DashboardTrackerConfig> dashboardTrackerConfigs)
        {
            var deletedColumnsList = new List<ColumnsDto>();
            var allDeletedColumnsData = new List<Dictionary<string, object>>();

            foreach (var config in dashboardTrackerConfigs)
            {
                if (!string.IsNullOrEmpty(config.DeletedColumns))
                {
                    var deletedColumnList = config.DeletedColumns.Split(',').ToList();

                    foreach (var columnName in deletedColumnList)
                    {
                        if (!string.IsNullOrEmpty(columnName.Trim()))
                        {
                            if (config.FieldType == (int)FieldTypeEnum.TimeSeries)
                            {
                                var periodTexts = DashboardTrackerHelper.GeneratePeriodTexts(config.StartPeriod, config.EndPeriod, config.FrequencyType.Value, config.TimeSeriesDateFormat);
                                var displayText = periodTexts.FirstOrDefault(f => f.Value.Equals(columnName))?.DisplayText;
                                var column = new ColumnsDto
                                {
                                    ID = config.ID,
                                    FieldType = config.FieldType,
                                    DataType = config.DataType,
                                    Name = (bool)config.IsPrefix ? $"{displayText} - {config.Name}" : $"{config.Name} - {displayText}",
                                    IsTimeSeries = true,
                                    TimeSeriesID = columnName,
                                    IsDropDown = config.DataType == (int)DataTypeEnum.Dropdown
                                };
                                deletedColumnsList.Add(column);
                            }
                            else
                            {
                                var column = new ColumnsDto
                                {
                                    ID = config.ID,
                                    FieldType = config.FieldType,
                                    DataType = config.DataType,
                                    Name = config.Name,
                                    IsTimeSeries = false,
                                    TimeSeriesID = null,
                                    IsDropDown = config.DataType == (int)DataTypeEnum.Dropdown,
                                    MapTo = config.MapTo,
                                    MapToType = config.MaptoType
                                };
                                deletedColumnsList.Add(column);
                            }
                        }
                    }
                }
            }

            return deletedColumnsList;
        }
    }
}

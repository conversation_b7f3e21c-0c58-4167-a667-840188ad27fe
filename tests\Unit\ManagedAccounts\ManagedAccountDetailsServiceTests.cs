using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using ManagedAccounts.Services;
using DataAccessLayer.DBModel;
using DataAccessLayer.ManagedAccounts;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace ManagedAccounts.UnitTest
{
    public class ManagedAccountDetailsServiceTests : IDisposable
    {
        private readonly DbContextOptions<DBEntities> _options;
        private readonly DBEntities _context;
        private readonly Mock<ILogger<ManagedAccountDetailsService>> _mockLogger;
        private readonly ManagedAccountDetailsService _service;

        public ManagedAccountDetailsServiceTests()
        {
            _options = new DbContextOptionsBuilder<DBEntities>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new DBEntities(_options);
            _mockLogger = new Mock<ILogger<ManagedAccountDetailsService>>();
            _service = new ManagedAccountDetailsService(_context, _mockLogger.Object);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_WhenNameDoesNotExist_ReturnsFalse()
        {
            // Arrange
            var managedAccountName = "Unique Account Name";

            // Act
            var result = await _service.CheckDuplicateNameAsync(managedAccountName);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_WhenNameExists_ReturnsTrue()
        {
            // Arrange
            var managedAccountName = "Existing Account";
            var existingAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = managedAccountName,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.Add(existingAccount);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CheckDuplicateNameAsync(managedAccountName);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_WhenNameExistsButDeleted_ReturnsFalse()
        {
            // Arrange
            var managedAccountName = "Deleted Account";
            var deletedAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = managedAccountName,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = true // Marked as deleted
            };

            _context.ManagedAccountDetails.Add(deletedAccount);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CheckDuplicateNameAsync(managedAccountName);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_WhenNameExistsButInactive_ReturnsFalse()
        {
            // Arrange
            var managedAccountName = "Inactive Account";
            var inactiveAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = managedAccountName,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = false, // Marked as inactive
                IsDeleted = false
            };

            _context.ManagedAccountDetails.Add(inactiveAccount);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CheckDuplicateNameAsync(managedAccountName);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_WithExcludeId_ExcludesSpecifiedRecord()
        {
            // Arrange
            var managedAccountName = "Test Account";
            var accountId = Guid.NewGuid();
            var existingAccount = new ManagedAccountDetails
            {
                ManagedAccountID = accountId,
                ManagedAccountName = managedAccountName,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.Add(existingAccount);
            await _context.SaveChangesAsync();

            // Act - Exclude the existing account ID
            var result = await _service.CheckDuplicateNameAsync(managedAccountName, accountId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_CaseInsensitive_ReturnsTrue()
        {
            // Arrange
            var existingAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Test Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.Add(existingAccount);
            await _context.SaveChangesAsync();

            // Act - Check with different case
            var result = await _service.CheckDuplicateNameAsync("TEST ACCOUNT");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CheckDuplicateNameAsync_TrimsWhitespace_ReturnsTrue()
        {
            // Arrange
            var existingAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Test Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.Add(existingAccount);
            await _context.SaveChangesAsync();

            // Act - Check with whitespace
            var result = await _service.CheckDuplicateNameAsync("  Test Account  ");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetAllAsync_ReturnsAllActiveAccounts()
        {
            // Arrange
            var account1 = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Account 1",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            var account2 = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Account 2",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.AddRange(account1, account2);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Contains(result, a => a.ManagedAccountName == "Account 1");
            Assert.Contains(result, a => a.ManagedAccountName == "Account 2");
        }

        [Fact]
        public async Task GetAllAsync_ExcludesDeletedAccounts()
        {
            // Arrange
            var activeAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Active Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            var deletedAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Deleted Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = true
            };

            _context.ManagedAccountDetails.AddRange(activeAccount, deletedAccount);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("Active Account", result.First().ManagedAccountName);
        }

        [Fact]
        public async Task GetAllAsync_ExcludesInactiveAccounts()
        {
            // Arrange
            var activeAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Active Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            var inactiveAccount = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Inactive Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = false,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.AddRange(activeAccount, inactiveAccount);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("Active Account", result.First().ManagedAccountName);
        }

        [Fact]
        public async Task GetAllAsync_ReturnsEmptyWhenNoAccounts()
        {
            // Act
            var result = await _service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetAllAsync_OrdersByManagedAccountName()
        {
            // Arrange
            var accountZ = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "Z Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            var accountA = new ManagedAccountDetails
            {
                ManagedAccountID = Guid.NewGuid(),
                ManagedAccountName = "A Account",
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false
            };

            _context.ManagedAccountDetails.AddRange(accountZ, accountA);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal("A Account", result.First().ManagedAccountName);
            Assert.Equal("Z Account", result.Last().ManagedAccountName);
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}

ALTER   PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig] ( @FeatureId INT ) AS
BEGIN   IF (@FeatureId = 14)
BEGIN
   DECLARE @Order TABLE (SubFeatureID INT, DisplayOrder INT);
        INSERT INTO @Order (SubFeatureID, DisplayOrder)
        VALUES
            (2, 1), (3, 2), (4, 3), (5, 4), (6, 5), (7, 6), (9, 7), (10, 8), (11, 9), (12, 10),
            (13, 11), (14, 12), (15, 13), (16, 14), (17, 15), (18, 16), (94, 17), (95, 18), (96, 19),
            (97, 20), (98, 21), (99, 22), (100, 23), (101, 24), (102, 25), (103, 26), (104, 27),
            (105, 28), (106, 29), (107, 30), (108, 31), (55, 32), (56, 33), (57, 34), (58, 35),(125,36),(126,37),(127,38),(128,39),(129,40),
            (59, 41), (60, 42), (61, 43), (62, 44), (63, 45), (64, 46), (65, 47), (66, 48),
            (67, 49), (68, 50),(130, 51),(131, 52),(132, 53),(133, 54),(134, 55),
			(69, 56);
 
        SELECT
            f.SubFeatureID,
            f.SubFeature AS SubFeatureName,
            COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
            f.isDeleted AS IsDeleted,
            f.ParentFeatureID AS ParentFeatureId,
			o.DisplayOrder
        FROM M_SubFeature f
        LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4, (SELECT SubPageID FROM M_SubPageDetails WHERE Name='Documents' AND PageID=1)) AND s.isDeleted = 0
        LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38, 41, 47) AND sf.isDeleted = 0
        LEFT JOIN @Order o ON f.SubFeatureID = o.SubFeatureID
        WHERE (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) AND ParentFeatureID = 14
        ORDER BY o.DisplayOrder
 END
ELSE IF(@FeatureId = 13)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName,
   COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted, ParentFeatureId
   FROM       M_SubFeature f     LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=2)) AND s.isDeleted = 0
   LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN ((Select SubPageID from M_SubPageDetails WHERE Name='Fund Key Performance Indicator' AND PageID=2),(Select SubPageID from M_SubPageDetails WHERE Name='Fund Financials' AND PageID=2)) AND sf.isDeleted = 0
   LEFT JOIN @Order o ON f.SubFeatureID = o.SubFeatureID
   WHERE (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and ParentFeatureID = 13
   ORDER BY f.SubFeatureID
   END
ELSE IF(@FeatureId = 15)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL
END
ELSE IF(@FeatureId = 50)
BEGIN
SELECT  f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId     FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
END
ELSE IF(@FeatureId = 42)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM   M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
 
UNION ALL
 
SELECT    f.SubFeatureID,      f.SubFeature As SubFeatureName,COALESCE(f.SubFeature, f.SubFeature) AS SubFeatureAliasName,f.isDeleted AS IsDeleted,f.ParentFeatureID AS ParentFeatureId     FROM M_SubFeature f
WHERE ParentFeatureID = 42 AND F.isDeleted=0 AND PageConfigName IS NULL
END
ELSE IF(@FeatureId = 19)
BEGIN
SELECT       SubFeatureID,      SubFeature As SubFeatureName, COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,isDeleted AS IsDeleted,
ParentFeatureID AS ParentFeatureId  FROM  M_SubFeature
WHERE  SubFeatureID  BETWEEN 33 AND 48
END
ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
GO
ALTER  PROCEDURE [dbo].[GetMasterkpiNotMappedList]
(
@companyId INT,
@Type Varchar(100),
@ModuleId INT = NULL
)
AS
BEGIN  
	IF @Type = 'Company KPI' 
							BEGIN
							SELECT DISTINCT(M.CompanyKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId', M.KpiInfo 'KpiInfo',M.Synonym,M.Description   FROM M_CompanyKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioCompanyKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CompanyKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MappingPortfolioCompanyKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Impact KPI'
							BEGIN
							SELECT DISTINCT(M.ImpactKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ImpactKPI M 
							LEFT JOIN (SELECT *FROM Mapping_ImpactKPI_Order 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ImpactKPIID = MAP.ImpactKPIID 
							WHERE M.IsDeleted =0  AND MAP.ImpactKPIMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Investment KPI'
							BEGIN
							SELECT DISTINCT(M.InvestmentKPIId) 'Id',M.KPI 'Name', M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_InvestmentKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioInvestmentKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.InvestmentKPIId = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioInvestmentKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Balance Sheet KPI'
							BEGIN
							SELECT DISTINCT(M.BalanceSheetLineItemID) 'Id',M.BalanceSheetLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_BalanceSheet_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyBalanceSheetLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.BalanceSheetLineItemID = MAP.BalanceSheetLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyBalanceSheetLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Cashflow KPI'
							BEGIN
							SELECT DISTINCT(M.CashFlowLineItemID) 'Id',M.CashFlowLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_CashFlow_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyCashFlowLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CashFlowLineItemID = MAP.CashFlowLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyCashFlowLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Profit & Loss KPI'
							BEGIN
							SELECT DISTINCT(M.ProfitAndLossLineItemID) 'Id',M.ProfitAndLossLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ProfitAndLoss_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyProfitAndLossLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ProfitAndLossLineItemID = MAP.ProfitAndLossLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyProfitAndLossLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Operational KPI'
							BEGIN
							SELECT DISTINCT(M.SectorwiseOperationalKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_SectorwiseOperationalKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioOperationalKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.SectorwiseOperationalKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioOperationalKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Credit KPI'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 2) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 2  AND Mapping_KpisID IS NULL order by Name
							END
	ELSE IF @Type = 'Trading Records'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 1) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 1  AND Mapping_KpisID IS NULL order by Name
							END
    IF @ModuleId = 16
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKpiId 'FormulaKPIId',M.KpiInfo 'KpiInfo'  FROM MMonthlyReport M 
							LEFT JOIN (SELECT *FROM MappingMonthlyReport 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.KpiId = MAP.KpiId
							WHERE M.IsDeleted =0 AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45)
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',KpiTypeId,M.Synonym,M.Description  FROM MCapTable M 
							LEFT JOIN (SELECT *FROM MappingCapTable 
							where PortfolioCompanyId = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.KpiId = MAP.KpiId 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30,46,47,48,49,50,51,52,53,54,55)
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND Mapping_KpisID IS NULL order by Name
							END
END
GO
ALTER PROCEDURE [dbo].[ProcCopyKPIToCompanies]
(
@KpiType VARCHAR(100),
@CompanyId INT,
@UserId INT,
@CompanyIds NVARCHAR(MAX),
@ModuleId INT = NULL
)
AS
BEGIN
SET NOCOUNT ON
			BEGIN TRY
			DECLARE @RowCount INT,@Row INT = 1;
			DROP TABLE IF EXISTS #tempCopyToCompany
			CREATE TABLE #tempCopyToCompany
			(
				Id INT IDENTITY(1,1) PRIMARY KEY,
				PortfolioCompanyID Int
			)
			INSERT INTO #tempCopyToCompany(PortfolioCompanyID)
			SELECT Item AS PortfolioCompanyID FROM[dbo].[SplitString](@CompanyIds,',') WHERE Item!=@CompanyId
			SET @RowCount = (SELECT Count(PortfolioCompanyID) FROM #tempCopyToCompany)
			WHILE (@Row <= @RowCount)
			BEGIN
			DROP TABLE IF EXISTS #KPIHierarchy;
            DROP TABLE IF EXISTS #ExistingKPIs;
			CREATE TABLE #ExistingKPIs ( KPIName VARCHAR(500), DestKpiID INT, ParentKPIID INT, ParentKPIName VARCHAR(500), DisplayOrder INT, IsRootLevel BIT ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			CREATE TABLE #KPIHierarchy ( SourceKpiID INT, KPIName VARCHAR(MAX), ParentKPIID INT, ParentKPIName VARCHAR(MAX), Level INT, DisplayOrder INT, HierarchyOrder VARCHAR(MAX), NewDisplayOrder INT, KPITypeID INT ,Formula NVarchar(Max),FormulaKPIId INT,CreatedBy INT,CreatedOn datetime,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @PortfolioCompanyID INT
			SELECT @PortfolioCompanyID=PortfolioCompanyID FROM #tempCopyToCompany Where ID= @Row
			DECLARE @MappingOperational table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCompany table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingImpact table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ImpactKPIID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,KPIOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingInvestment table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),KPIHierarchy Nvarchar(Max),HierarchyOrder NVARCHAR(500),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingBalance table(ID INT IDENTITY(1,1), PortfolioCompanyID int,BalanceSheetLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingProfit table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ProfitAndLossLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCashflow table(ID INT IDENTITY(1,1), PortfolioCompanyID int,CashFlowLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingTrading table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCredit table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCapTable table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingMonthlyReport table(ID INT IDENTITY(1,1),PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,Formula nvarchar(max),FormulaKPIId nvarchar(max));
			DELETE FROM @MappingOperational
			DELETE FROM @MappingCompany
			DELETE FROM @MappingImpact
			DELETE FROM @MappingInvestment
			DELETE FROM @MappingBalance
			DELETE FROM @MappingProfit
			DELETE FROM @MappingCashflow
			DELETE FROM @MappingTrading
			DELETE FROM @MappingCredit
			DELETE FROM @MappingCapTable
			DELETE FROM @MappingMonthlyReport
			Declare @MaxOrderId INT;
			Declare @ID INT,@ParentKPIID INT,@DestParentKPIID INT,@KpiID INT;
			Declare @KPI NVarchar(MAX)=NULL;
			IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO @MappingOperational 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioOperationalKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(SELECT * FROM Mapping_PortfolioOperationalKPI  WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingOperational ))
					BEGIN
						UPDATE Mapping_PortfolioOperationalKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioOperationalKPI A Inner Join @MappingOperational B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID 
					END							
				INSERT INTO Mapping_PortfolioOperationalKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction FROM  @MappingOperational WHERE  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioOperationalKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Company KPI')
			BEGIN
			 	INSERT INTO @MappingCompany 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioCompanyKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_PortfolioCompanyKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and KpiID IN (SELECT KpiID FROM @MappingCompany ))
					BEGIN
						UPDATE Mapping_PortfolioCompanyKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioCompanyKPI A Inner Join @MappingCompany B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_PortfolioCompanyKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCompany where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioCompanyKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Impact KPI')
			BEGIN
			 	INSERT INTO @MappingImpact 
				SELECT PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_ImpactKPI_Order where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_ImpactKPI_Order where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and ImpactKPIID IN (SELECT ImpactKPIID FROM @MappingImpact ))
					BEGIN
						UPDATE Mapping_ImpactKPI_Order SET KPIOrder=B.KPIOrder,ImpactKPIID=B.ImpactKPIID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_ImpactKPI_Order A Inner Join @MappingImpact B On A.ImpactKPIID=B.ImpactKPIID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_ImpactKPI_Order(PortfolioCompanyID,ImpactKPIID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),0,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingImpact where ImpactKPIID NOT IN (SELECT ImpactKPIID FROM Mapping_ImpactKPI_Order WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
			END
			IF(@KpiType = 'Investment KPI')
			BEGIN	
				SELECT @MaxOrderId = ISNULL(MAX(DisplayOrder), 0) FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @PortfolioCompanyID;
				;WITH KPIHierarchyCTE AS (
                    SELECT 
                        src.KpiID AS SourceKpiID,
                         CAST(srcKPI.KPI AS VARCHAR(MAX)) AS KPIName,
                        src.ParentKPIID,
                         CAST(NULL AS VARCHAR(MAX)) AS ParentKPIName,
                        0 AS Level,
                        src.DisplayOrder,
						CAST(RIGHT('000000' + CAST(DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) AS HierarchyOrder,
						src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.ParentKPIID IS NULL
                    AND src.IsDeleted = 0
                    UNION ALL
                    SELECT 
                        src.KpiID,
                        CAST(srcKPI.KPI AS VARCHAR(MAX)),
                        src.ParentKPIID,
                        CAST(parentKPI.KPI AS VARCHAR(MAX)),
                        h.Level + 1,
                        src.DisplayOrder,
						 CAST(h.HierarchyOrder + '.' + RIGHT('000000' + CAST(src.DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) as HierarchyOrder,
						 src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    INNER JOIN M_InvestmentKPI parentKPI 
                        ON src.ParentKPIID = parentKPI.InvestmentKPIId
                    INNER JOIN KPIHierarchyCTE h 
                        ON src.ParentKPIID = h.SourceKpiID
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.IsDeleted = 0
                )
				 INSERT INTO #KPIHierarchy(KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction)
                SELECT KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,CASE WHEN @MaxOrderId=0 THEN
				        ROW_NUMBER() OVER (ORDER BY HierarchyOrder) 
						ELSE @MaxOrderId+1 + ROW_NUMBER() OVER (ORDER BY HierarchyOrder) END AS NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction FROM KPIHierarchyCTE;

				-- Get existing destination KPIs
                INSERT INTO #ExistingKPIs
                SELECT DISTINCT
                    destKPI.KPI,
                    dest.KpiID,
                    dest.ParentKPIID,
                    parentKPI.KPI,
                    dest.DisplayOrder,
                    CASE WHEN dest.ParentKPIID IS NULL THEN 1 ELSE 0 END,
					dest.Synonym,
					dest.Definition,
					dest.IsExtraction
                FROM Mapping_PortfolioInvestmentKPI dest
                INNER JOIN M_InvestmentKPI destKPI 
                    ON dest.KpiID = destKPI.InvestmentKPIId
                LEFT JOIN M_InvestmentKPI parentKPI 
                    ON dest.ParentKPIID = parentKPI.InvestmentKPIId
                WHERE dest.PortfolioCompanyID = @PortfolioCompanyID
                AND dest.IsDeleted = 0;

				--SELECT * FROM #KPIHierarchy

				INSERT INTO @MappingInvestment(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,HierarchyOrder,Synonym,Definition,IsExtraction) 
				SELECT 
                    kh.KPITypeID,
                    @PortfolioCompanyID,
                    kh.SourceKpiID,
                    @UserId,
                    GETDATE(),
                    0,
                    CASE 
					    WHEN kh.ParentKPIID IS NOT NULL THEN
					        CASE WHEN EXISTS (
					            SELECT 1 
					            FROM #ExistingKPIs e 
					            WHERE e.KPIName = kh.ParentKPIName
					            AND e.IsRootLevel = 1
					        )
					        THEN 
					            (SELECT TOP 1 e.DestKpiID 
					             FROM #ExistingKPIs e 
					             WHERE e.KPIName = kh.ParentKPIName
					             AND e.IsRootLevel = 1)
					        ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					        END
					    ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					END ParentKPIID,
					kh.NewDisplayOrder,kh.Formula,kh.FormulaKPIId,kh.HierarchyOrder,kh.Synonym,kh.Definition,kh.IsExtraction
                FROM #KPIHierarchy kh
                LEFT JOIN #ExistingKPIs existing
                    ON existing.KPIName = kh.KPIName
                    AND existing.IsRootLevel = CASE WHEN kh.ParentKPIID IS NULL THEN 1 ELSE 0 END
                WHERE NOT EXISTS (
                    SELECT 1 FROM #ExistingKPIs e
                    WHERE e.KPIName = kh.KPIName
                    AND (
                        (kh.ParentKPIID IS NULL AND e.IsRootLevel = 1) OR
                        (kh.ParentKPIID IS NOT NULL AND e.ParentKPIName = kh.ParentKPIName)
                    )
                );
			   IF EXISTS(select *from Mapping_PortfolioInvestmentKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingInvestment))
				BEGIN
					UPDATE Mapping_PortfolioInvestmentKPI SET DisplayOrder=B.DisplayOrder ,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioInvestmentKPI A Inner Join @MappingInvestment B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
			   END
			   INSERT INTO Mapping_PortfolioInvestmentKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
			   SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingInvestment where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioInvestmentKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
				DROP TABLE IF EXISTS #KPIHierarchy;
                DROP TABLE IF EXISTS #ExistingKPIs;
			
			END
			IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
			 	INSERT INTO @MappingBalance 
				SELECT PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyBalanceSheetLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_CompanyBalanceSheetLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and BalanceSheetLineItemID IN (SELECT BalanceSheetLineItemID FROM @MappingBalance))
					BEGIN
						UPDATE Mapping_CompanyBalanceSheetLineItems SET DisplayOrder=B.DisplayOrder,BalanceSheetLineItemID=B.BalanceSheetLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyBalanceSheetLineItems A Inner Join @MappingBalance B On A.BalanceSheetLineItemID=B.BalanceSheetLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyBalanceSheetLineItems(PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingBalance where  BalanceSheetLineItemID NOT IN (SELECT BalanceSheetLineItemID FROM Mapping_CompanyBalanceSheetLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO @MappingCashflow 
				SELECT PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyCashFlowLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyCashFlowLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and CashFlowLineItemID IN (SELECT CashFlowLineItemID FROM @MappingCashflow))
					BEGIN
						UPDATE Mapping_CompanyCashFlowLineItems SET DisplayOrder=B.DisplayOrder,CashFlowLineItemID=B.CashFlowLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyCashFlowLineItems A Inner Join @MappingCashflow B On A.CashFlowLineItemID=B.CashFlowLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyCashFlowLineItems(PortfolioCompanyID,CashFlowLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCashflow where  CashFlowLineItemID NOT IN (SELECT CashFlowLineItemID FROM Mapping_CompanyCashFlowLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Profit & Loss KPI')
			BEGIN
				INSERT INTO @MappingProfit 
				SELECT PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyProfitAndLossLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyProfitAndLossLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ProfitAndLossLineItemID IN (SELECT ProfitAndLossLineItemID FROM @MappingProfit))
					BEGIN
						UPDATE Mapping_CompanyProfitAndLossLineItems SET DisplayOrder=B.DisplayOrder,ProfitAndLossLineItemID=B.ProfitAndLossLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyProfitAndLossLineItems A Inner Join @MappingProfit B On A.ProfitAndLossLineItemID=B.ProfitAndLossLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyProfitAndLossLineItems(PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingProfit where  ProfitAndLossLineItemID NOT IN (SELECT ProfitAndLossLineItemID FROM Mapping_CompanyProfitAndLossLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
			 	INSERT INTO @MappingTrading 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=1 AND IsDeleted = 0
				
				IF EXISTS(select *from Mapping_Kpis where ModuleID=1 and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingTrading))
					BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingTrading B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=1
					END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingTrading where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=1 )
												
			END
		IF(@KpiType = 'Credit KPI' OR @ModuleId IN (2, 17,18,19,20,21,22,23,24,25,26,27,28,29,30,46,47,48,49,50,51,52,53,54,55))
			BEGIN
			 	INSERT INTO @MappingCredit 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_Kpis where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingCredit))
				BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingCredit B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=@ModuleId
				END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCredit where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=@ModuleId )												
			END
			IF(@ModuleId = 16)
			BEGIN
			 	INSERT INTO @MappingMonthlyReport 
				SELECT PortfolioCompanyId,KpiId,CreatedBy,GETUTCDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKpiId From MappingMonthlyReport where PortfolioCompanyId = @CompanyId  AND IsDeleted = 0
				IF EXISTS(select *from MappingMonthlyReport where  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingMonthlyReport))
				BEGIN
						UPDATE MappingMonthlyReport SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKpiId,Formula=B.Formula,FormulaKpiId=B.FormulaKpiId FROM MappingMonthlyReport A Inner Join @MappingMonthlyReport B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID
				END							
				INSERT INTO MappingMonthlyReport(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKPIId) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETUTCDATE(),0,ParentKPIID,DisplayOrder,IsHeader,Formula,FormulaKPIId From @MappingMonthlyReport where  KpiID NOT IN (SELECT KpiID FROM MappingMonthlyReport WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID)												
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
			 	INSERT INTO @MappingCapTable 
				SELECT 1,PortfolioCompanyId,KpiId,CreatedBy,GETDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From MappingCapTable where PortfolioCompanyId = @CompanyId  AND ModuleId=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from MappingCapTable where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingCapTable))
				BEGIN
						UPDATE MappingCapTable SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM MappingCapTable A Inner Join @MappingCapTable B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID AND A.ModuleId=@ModuleId
				END							
				INSERT INTO MappingCapTable(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCapTable where  KpiID NOT IN (SELECT KpiID FROM MappingCapTable WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and ModuleId=@ModuleId)												
			END
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempCopyToCompany		
			END TRY
			BEGIN CATCH
              IF OBJECT_ID('tempdb..#KPIHierarchy') IS NOT NULL DROP TABLE #KPIHierarchy;
			  IF OBJECT_ID('tempdb..#ExistingKPIs') IS NOT NULL DROP TABLE #ExistingKPIs;
			END CATCH
END
GO
ALTER PROCEDURE [dbo].[spFundKpiValues](
	@FundId int=0,
	@dataType varchar(10) = 'M',
	@ValueTypeId int=0,
	@ModuleId int=0
	)
	AS
	BEGIN
	DECLARE @ValueType varchar(10) = 'Actual'
	SET @ValueType = (select HeaderValue from M_ValueTypes where ValueTypeID =@ValueTypeId)
	DECLARE @TableValues table(IdentityId int  NULL,ValuesKpiId int  NULL,  
							 KPIActualValue varchar(Max) NULL, Year int NULL, Quarter nvarchar(100) NULL, Month int  NULL, Data varchar(10),			
							 CompanyId int  NULL,MappingKpisID int NULL, Half nvarchar(10) NULL)						 
	DECLARE @TableMappings table(KpiID int  NULL,
							 ParentKPIID int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo nvarchar(100) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,MappingFundSectionKpiID int NULL,
							 MethodologyId INT NULL)
	DECLARE @TempTable table(KpiId int  NULL,
							 ParentId int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo Varchar(max) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,
							 KPIActualValue varchar(Max) NULL,
							 Quarter	nvarchar(100) NULL,
							 Year int NULL,
							 Month int  NULL,
							 CompanyId int  NULL,
							 ValuesKpiId int  NULL,
							 IdentityId int  NULL,
							 MethodologyId INT NULL,
							 Half nvarchar(10) NULL)
						 
	DECLARE @TempTable_Match table(KpiId int  NULL,
							 ParentId int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo nvarchar(100) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,
							 KPIActualValue varchar(Max) NULL,
							 Quarter nvarchar(100) NULL,
							 Year int NULL,
							 Month int  NULL,
							 CompanyId int  NULL,
							 ValuesKpiId int  NULL,
							 IdentityId int  NULL,
							 MethodologyId INT NULL,
							 Half nvarchar(10) NULL)
	INSERT INTO @TableValues
	SELECT IdentityId,KpiId,KPIValue, Year, Quarter, Month, Data,CompanyId,MappingId, Half FROM (Select A.FundMasterKpiValueId IdentityId,Map.KpiID KpiId,A.KPIValue,A.Year,A.Quarter,A.Month,
	CASE 
	WHEN (Month IS NULL OR Month=0) and Quarter IS NULL and Half IS NULL THEN 'A'
	WHEN (Month IS NULL OR Month=0) and Quarter IS NULL and Half IS NOT NULL THEN 'H'
	WHEN (Month IS NULL OR Month=0) and Quarter IS NOT NULL THEN 'Q'
	WHEN Quarter IS NULL and Half IS NULL THEN 'M'
	END Data,A.FundId CompanyId,A.MappingId,A.Half
	FROM FundMasterKpiValues A 
	INNER JOIN MappingFundSectionKpi Map on A.MappingId=Map.MappingFundSectionKpiId
	WHERE A.IsDeleted=0 and Map.IsDeleted=0 AND A.FundId=@FundId and A.ModuleID=@ModuleId and A.ValueTypeID=@ValueTypeId) C WHERE C.Data=@dataType

	INSERT INTO @TableMappings
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.FundId,Matr.KPI,Matr.KPIInfo,Matr.IsBoldKPI,Matr.IsHeader,Map.MappingFundSectionKpiID,Matr.MethodologyId
	FROM (SELECT DISTINCT OI.KpiID,OI.ParentKPIID,OI.DisplayOrder,OI.FundId,OI.MappingFundSectionKpiID
		FROM MappingFundSectionKpi OI
		  LEFT JOIN MappingFundSectionKpi OI2
			  ON OI2.ParentKPIID = OI.KpiID  WHERE OI.IsDeleted=0 and OI.ModuleID=@ModuleId and OI.FundId=@FundId  ) Map 
	INNER JOIN MFundSectionKpi Matr ON Map.KpiID=Matr.FundSectionKpiId  
	WHERE Matr.IsDeleted=0 

	INSERT INTO @TempTable
	SELECT MappingTable.KpiID KpiId,ISNULL(MappingTable.ParentKPIID,0) ParentId,MappingTable.DisplayOrder,MappingTable.PortfolioCompanyID,
		   MappingTable.KPI,MappingTable.KPIInfo,MappingTable.IsBoldKPI,MappingTable.IsHeader,
		   ValuesTable.KPIActualValue,ValuesTable.Quarter,ValuesTable.Year,ValuesTable.Month,ValuesTable.CompanyId,ValuesTable.ValuesKpiID ValuesKpiId,ValuesTable.IdentityId,MappingTable.MethodologyId,ValuesTable.Half FROM ( 
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.PortfolioCompanyID,Map.KPI,Map.KPIInfo,Map.IsBoldKPI,Map.IsHeader,MappingFundSectionKpiID,Map.MethodologyId FROM @TableMappings Map) MappingTable LEFT OUTER JOIN  
	(
	select IdentityId,ValuesKpiId,KPIActualValue,Quarter,Year,Month,CompanyId,MappingKpisID,Half FROM @TableValues WHERE Data=@dataType
	) ValuesTable on MappingTable.MappingFundSectionKpiID=ValuesTable.MappingKpisID  ORDER BY DisplayOrder

	INSERT INTO @TempTable_Match
	Select DISTINCT C.KpiId,C.ParentId,C.DisplayOrder,C.PortfolioCompanyID,C.KPI,C.KPIInfo,C.IsBoldKPI,C.IsHeader,C.KPIActualValue,A.Quarter,A.Year,A.Month,C.PortfolioCompanyID CompanyId,C.ValuesKpiId,C.IdentityId,C.MethodologyId,C.Half FROM @TempTable A inner join (Select * FROM @TempTable WHERE Year IS NULL AND Quarter IS NULL AND (Month IS NULL OR Month=0) AND CompanyId IS NULL) C On A.ParentId=C.KpiId 

	INSERT INTO @TempTable_Match
	SELECT MappingTable.KpiID KpiId,ISNULL(MappingTable.ParentKPIID,0) ParentId,MappingTable.DisplayOrder,MappingTable.PortfolioCompanyID,
		   MappingTable.KPI,MappingTable.KPIInfo,MappingTable.IsBoldKPI,MappingTable.IsHeader,
		   ValuesTable.KPIActualValue,ValuesTable.Quarter,ValuesTable.Year,ValuesTable.Month,ValuesTable.CompanyId,ValuesTable.ValuesKpiID ValuesKpiId,ValuesTable.IdentityId,MappingTable.MethodologyId,ValuesTable.Half FROM ( 
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.PortfolioCompanyID,Map.KPI,Map.KPIInfo,Map.IsBoldKPI,Map.IsHeader,Map.MappingFundSectionKpiID,Map.MethodologyId FROM @TableMappings Map) MappingTable INNER JOIN 
	(
	select IdentityId,ValuesKpiId,KPIActualValue,Month,Quarter,Year,CompanyId,MappingKpisID,Half FROM @TableValues WHERE Data=@dataType
	) ValuesTable on MappingTable.MappingFundSectionKpiID=ValuesTable.MappingKpisID Order By ValuesKpiId ASC,Year asc,CONVERT(INT, Replace(Quarter,'Q', '')) asc , Month asc

	SELECT KpiId,ParentId,DisplayOrder,PortfolioCompanyID,KPI,KPIInfo,IsBoldKPI,IsHeader,KPIActualValue as KPIValue,Month,Quarter,Year,CompanyId,
	ValuesKpiId,IdentityId,MethodologyId,Half,dbo.AuditLogFundKPIFunction(PortfolioCompanyID,IdentityId) ActualAuditLog
	FROM @TempTable_Match  
	WHERE Year IS NOT NULL  ORDER BY DisplayOrder,Year,CONVERT(INT, Replace(Quarter,'Q', '')),Month
END
GO
ALTER   PROCEDURE [dbo].[spBulkUploadFundFinancials]  
 @TableName VARCHAR(100)
,@UserID INT
,@DocumentId INT = NULL
    ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
    ,@CommentId INT = NULL
,@FundId INT
,@IsIngestion BIT = 0
,@ProcessId NVARCHAR(1000) = NULL
AS
BEGIN
SET NOCOUNT ON;
DECLARE @SQLString NVARCHAR(MAX);
DECLARE @uploadType NVARCHAR(MAX)
IF @IsIngestion = 1
SET @uploadType = 'Ingestion'
ELSE
SET @uploadType = 'File Upload'
DECLARE @KpiTable TABLE (
    [Id] [INT],
[KpiId][int], 
[Quarter] [nvarchar](10) NULL,
[Year] [int] NOT NULL,
[Month] [int] NULL,
[KpiValue] NVARCHAR(MAX) NULL,
[ValueTypeId] INT NULL,
[ModuleId] [INT],
[Half] [nvarchar](10) NULL
)
SET @SQLString = 'SELECT Id,KpiId,Quarter,Year,Month,KpiValue,ValueTypeId,ModuleId,Half FROM ' + @TableName + '';
INSERT INTO @KpiTable
EXEC sys.sp_executesql @SQLString;
BEGIN TRY
BEGIN TRANSACTION
--Audit Log Entry for Update--
---First entry for audit log OLD values for prod data
INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
FROM @KpiTable KPI
INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
AND V.ValueTypeID = T.ValueTypeID
AND ISNULL(V.Half, - 1) = ISNULL(KPI.Half, - 1)
LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
WHERE Map.FundId = @FundId  
AND V.ModuleID = KPI.ModuleId
AND V.MappingId IS NOT NULL 
AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

---insert audit log data before update for old values
INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,DocumentId,SupportingDocumentsId,CommentId)
SELECT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,V.KPIValue,KPI.KpiValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,@DocumentId,@SupportingDocumentsId,@CommentId 
FROM @KpiTable KPI
INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
AND V.ValueTypeID = T.ValueTypeID
AND ISNULL(V.Half, - 1) = ISNULL(KPI.Half, - 1)
WHERE 
Map.FundId = @FundId 
AND V.ModuleID = KPI.ModuleId
AND V.MappingId IS NOT NULL 
AND KPI.KpiValue IS NOT NULL

--Update existing data--
UPDATE V
SET V.KPIValue = KPI.KpiValue,V.DocumentId = @DocumentId,V.SupportingDocumentsId = @SupportingDocumentsId,V.CommentId =@CommentId, V.ProcessId = @ProcessId
FROM @KpiTable KPI
INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
AND V.ValueTypeID = T.ValueTypeID
AND ISNULL(V.Half, - 1) = ISNULL(KPI.Half, - 1)
WHERE 
 Map.FundId = @FundId 
AND V.ModuleID = KPI.ModuleId
AND V.MappingId IS NOT NULL 
    AND KPI.KpiValue IS NOT NULL

--Insert New Data---
INSERT INTO FundMasterKpiValues(FundId, MappingId, KPIValue, KPIInfo, Month, Year, Quarter, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, ValueTypeID,[ModuleID],DocumentId,SupportingDocumentsId,CommentId,ProcessId,Half)
SELECT @FundId, Map.MappingFundSectionKpiID, KPI.KpiValue, M.KpiInfo, KPI.Month, KPI.Year, KPI.Quarter, GETDATE(), @UserID, NULL, NULL, 0, T.ValueTypeID,KPI.ModuleId,@DocumentId,@SupportingDocumentsId,@CommentId,@ProcessId,KPI.Half
from @KpiTable KPI
INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
LEFT JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
AND V.ValueTypeID = T.ValueTypeID
AND ISNULL(V.Half, - 1) = ISNULL(KPI.Half, - 1)
WHERE Map.FundId = @FundId 
AND V.MappingId IS NULL 
AND KPI.KpiValue IS NOT NULL;

---insert audit log data after insert
INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
FROM @KpiTable KPI
INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
AND V.ValueTypeID = T.ValueTypeID
AND ISNULL(V.Half, - 1) = ISNULL(KPI.Half, - 1)
LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
WHERE Map.FundId = @FundId  
AND V.ModuleID = KPI.ModuleId
AND V.MappingId IS NOT NULL 
AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

COMMIT TRANSACTION
END TRY
BEGIN CATCH

DECLARE @Message varchar(MAX) = ERROR_MESSAGE(),
        @Number int = ERROR_NUMBER(),
        @State smallint = ERROR_STATE();

THROW 50000, @Message, @State;


ROLLBACK TRANSACTION
END CATCH
END
GO
ALTER PROCEDURE  [dbo].[ProcCreateDuplicateKPI]
(
@KpiType VARCHAR(100),
@KpiId INT,
@UserId INT,
@ModuleId INT=NULL,
@Id INT OUTPUT
)
AS
BEGIN
SET NOCOUNT ON
		 BEGIN TRY
		 IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO M_SectorwiseOperationalKPI(SectorID,KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT SectorID,KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Company KPI')
			BEGIN
				INSERT INTO M_CompanyKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_CompanyKPI WHERE CompanyKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Impact KPI')
			BEGIN
				INSERT INTO M_ImpactKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym FROM M_ImpactKPI WHERE ImpactKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Investment KPI')
			BEGIN
				INSERT INTO M_InvestmentKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsNumeric,OrderBy,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,0,0,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym FROM M_InvestmentKPI WHERE InvestmentKPIId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
				INSERT INTO M_BalanceSheet_LineItems(BalanceSheetLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT BalanceSheetLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO M_CashFlow_LineItems(CashFlowLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT CashFlowLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Profit And Loss KPI')
			BEGIN
				INSERT INTO M_ProfitAndLoss_LineItems(ProfitAndLossLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym) SELECT ProfitAndLossLineItem,KpiInfo,@UserId,GETDATE(),0,1,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,1,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Credit KPI')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,2,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId  = 16)
			BEGIN
				INSERT INTO MMonthlyReport(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId) SELECT Kpi,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId FROM MMonthlyReport WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
				INSERT INTO MCapTable(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym) SELECT KPI,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym FROM MCapTable WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30,46,47,48,49,50,51,52,53,54,55))
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
         END TRY
         BEGIN CATCH
             SET @Id = 0
         END CATCH
END
GO
ALTER PROCEDURE [dbo].[GetKpiSupportedCommentsDetails]
 (@ModuleId INT,
 @CompanyId int,
 @MappingId int,
 @KpiId int,
 @ValueType nvarchar(500),
 @Month int,
 @Year int,
 @Quarter NVARCHAR(MAX) = NULL,
 @Half NVARCHAR(10) = NULL
 )
AS
BEGIN
DECLARE @SQL NVARCHAR(MAX),@ValueTypeId INT = NULL;

IF(@KpiId >0 AND @ModuleId IN (1,2,17,18,19,20,21,22,23,24,25,26,27,28,29,30) ) -- Credit KPI -- Trading Record --CustomTables --OtherKpis
	BEGIN
	SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
	SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.PCMasterKpiValueID ValueId 
		FROM PCMasterKpiValues PL 
		LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
		WHERE PL.IsDeleted = 0 AND PL.MappingKpisID = (SELECT TOP 1 Mapping_KpisID FROM Mapping_Kpis where KpiID = @KpiId AND PortfolioCompanyID = @CompanyId AND ModuleID = @ModuleId) AND PL.ValueTypeID = @ValueTypeId AND PL.PortfolioCompanyID = @CompanyId';
	END

IF(@KpiId >0 AND @ModuleId = 3) -- Operational KPI
	BEGIN
	SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
	SET @SQL = 'SELECT PCO.DocumentId DocumentId, Cmt.Comments, PCO.CommentId CommentId, PCO.SupportingDocumentsId SupportingDocumentsId, PCO.PortfolioCompanyOperationalKPIValueID ValueId 
	FROM PortfolioCompanyOperationalKPIQuarters PL Inner Join PortfolioCompanyOperationalKPIValues as PCO on PL.PortfolioCompanyOperationalKPIQuarterID = PCO.PortfolioCompanyOperationalKPIQuarterID
	LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PCO.CommentId
	WHERE PL.IsDeleted = 0 AND PCO.SectorwiseOperationalKPIID = @KpiId AND PL.PortfolioCompanyID = @CompanyId AND PL.ValueTypeID = @ValueTypeId';
	END

IF(@KpiId >0 AND @ModuleId = 4) -- Investment KPI
BEGIN
SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.PCInvestmentKPIQuarterlyValueID ValueId 
	FROM PCInvestmentKPIQuarterlyValue PL 
	LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
	WHERE PL.IsDeleted = 0 AND PL.InvestmentKPIID = @KpiId AND PL.ValueTypeID = @ValueTypeId AND PL.PortfolioCompanyID = @CompanyId';
END

IF(@KpiId >0 AND @ModuleId = 5) -- Company KPI
	BEGIN
	SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
		IF(@ValueTypeId > 0 AND @ValueTypeId = 4)
			BEGIN
				SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.PCCompanyKPIMonthlyValueID ValueId 
				FROM PCCompanyKPIMonthlyValue PL 
				LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
				WHERE PL.IsDeleted = 0 AND PL.CompanyKPIID = @KpiId AND PL.PortfolioCompanyID = @CompanyId';
			END
		ELSE IF(@ValueTypeId > 0 AND @ValueTypeId = 5)
			BEGIN
				SET @SQL = 'SELECT PL.DocumentId DocumentId, (SELECT TOP 1 Comments FROM DocumentComments WHERE Id = PL.BudgetCommentId) Comments, PL.BudgetCommentId CommentId, PL.BudgetSupportingDocumentsId SupportingDocumentsId, PL.PCCompanyKPIMonthlyValueID ValueId 
				FROM PCCompanyKPIMonthlyValue PL 
				LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.BudgetCommentId
				WHERE PL.IsDeleted = 0 AND PL.CompanyKPIID = @KpiId AND PL.PortfolioCompanyID = @CompanyId';
			END
		ELSE IF (@ValueTypeId > 0 AND @ValueTypeId = 6)
			BEGIN
				SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.CompanyKPIForecastValueId ValueId 
				FROM CompanyKPIForecastValues PL 
				LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
				WHERE PL.IsDeleted = 0 AND PL.CompanyKPIID = @KpiId AND PL.PortfolioCompanyID = @CompanyId';
			END
	END

IF(@KpiId >0 AND @ModuleId = 6) -- Impact KPI
	BEGIN
	SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
	SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.PCImpactKPIQuarterlyValueID ValueId 
		FROM PCImpactKPIQuarterlyValue PL 
		LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
		WHERE PL.IsDeleted = 0 AND PL.ImpactKPIID = @KpiId AND PL.ValueTypeID = @ValueTypeId AND PL.PortfolioCompanyID = @CompanyId';
	END
IF(@KpiId >0 AND @ModuleId > 1000)
BEGIN
	SET @ValueTypeId = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType);
	SET @SQL = 'SELECT PL.DocumentId DocumentId, Cmt.Comments, PL.CommentId CommentId, PL.SupportingDocumentsId SupportingDocumentsId, PL.FundMasterKpiValueId ValueId 
		FROM FundMasterKpiValues PL 
		LEFT OUTER JOIN DocumentComments Cmt on Cmt.Id = PL.CommentId
		WHERE PL.IsDeleted = 0 AND PL.MappingId = (SELECT TOP 1 MappingFundSectionKpiId FROM MappingFundSectionKpi where KpiID = @KpiId AND FundId = @CompanyId AND ModuleID = @ModuleId) AND PL.ValueTypeID = @ValueTypeId AND PL.FundId = @CompanyId';
END
IF ((@Month IS NULL OR @Month=0) AND (@Quarter IS NOT NULL AND @Quarter <> ''))
	BEGIN
		SET @SQL = @SQL + ' AND PL.Quarter = @Quarter AND PL.Year = @Year';
	END
ELSE IF ((@Month <>0))
	BEGIN
		SET @SQL = @SQL + ' AND PL.Month = @Month AND PL.Year = @Year';
	END
ELSE IF ((@Half IS NOT NULL AND @Half <> '') AND (@Month IS NULL OR @Month=0) AND (@Quarter IS NULL OR @Quarter = ''))
	BEGIN
		SET @SQL = @SQL + ' AND (PL.Month IS NULL OR PL.Month =0) AND PL.Quarter IS NULL AND PL.Year = @Year AND PL.Half = @Half';
	END
ELSE IF ((@Month IS NULL OR @Month=0) and (@Quarter IS NULL OR @Quarter = ''))
	BEGIN
		SET @SQL = @SQL + ' AND (PL.Month IS NULL OR PL.Month =0) AND PL.Quarter IS NULL AND PL.Year = @Year';
	END
ELSE
	BEGIN
		SET @SQL ='';
	END
EXEC sp_executesql @SQL, N'@KpiId int, @Year int, @Month int, @Quarter NVARCHAR(MAX),@Half NVARCHAR(2), @ValueTypeId INT,@ModuleId INT,@CompanyId INT', @KpiId, @Year, @Month, @Quarter,@Half,@ValueTypeId,@ModuleId,@CompanyId;
END
GO
ALTER PROCEDURE [dbo].[ProcGetAuditLogs](
	@KpiId INT
	,@ModuleId INT
	,@AttributeId INT
	,@CompanyId INT
	,@ValueType VARCHAR(50)
	)
AS
BEGIN
	SET NOCOUNT ON;

	IF @ModuleId = 1
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'TradingRecords'
				) 'ModuleName'
			,MasterKpiID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_MasterKpis
		WHERE MasterKpiID = @KpiId
			AND ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM MasterKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,PCMasterKpiValueID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND PCMasterKpiValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 2
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CreditKPI'
				) 'ModuleName'
			,MasterKpiID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_MasterKpis
		WHERE MasterKpiID = @KpiId
			AND ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM MasterKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,PCMasterKpiValueID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND PCMasterKpiValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 3
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'OperationalKPIs'
				) 'ModuleName'
			,SectorwiseOperationalKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_SectorwiseOperationalKPI
		WHERE SectorwiseOperationalKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM DataAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Operational KPIs'
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,A.Description 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM DataAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND AttributeId = @AttributeId
				AND AttributeName = 'Operational KPIs'
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,SectorwiseOperationalKPIID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PortfolioCompanyOperationalKPIValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyOperationalKPIValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 4
	BEGIN
		SELECT (SELECT AliasName FROM M_SubPageFields WHERE Name='InvestmentKPIs')'ModuleName',InvestmentKPIId 'KpiId', KPI'Kpi',KpiInfo,(SELECT C.CurrencyCode FROM PortfolioCompanyDetails P INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
		WHERE P.PortfolioCompanyID =@CompanyId)AS CurrencyCode FROM M_InvestmentKPI WHERE InvestmentKPIId=@KpiId
	    IF EXISTS(SELECT TOP 1* FROM DataAuditLog WHERE  AttributeId = @AttributeId AND  PortfolioCompanyId = @CompanyId)
		BEGIN
			SELECT A.AuditId,AttributeId,OldValue,NewValue,Description'Source',D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,CommentId,U.FirstName +' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn' FROM DataAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE  PortfolioCompanyId =@CompanyId AND AttributeId = @AttributeId AND Comments = @ValueType ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0'AuditId',PCInvestmentKPIQuarterlyValueID 'AttributeId',NULL'OldValue',KPIActualValue'NewValue','Bulk Upload'Source,D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,SupportingDocumentsId,CommentId,U.FirstName +' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt')'CreatedOn' FROM PCInvestmentKPIQuarterlyValue A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE  PortfolioCompanyId =@CompanyId AND PCInvestmentKPIQuarterlyValueID = @AttributeId ORDER BY AuditId DESC
		END 
	END
	ELSE IF @ModuleId = 5
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CompanyKPIs'
				) 'ModuleName'
			,CompanyKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_CompanyKPI
		WHERE CompanyKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM DataAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
				)
		BEGIN
			IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'forecast'
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,PCCompanyKPIMonthlyValueID 'AttributeId'
					,NULL 'OldValue'
					,KPIActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCCompanyKPIMonthlyValue A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND PCCompanyKPIMonthlyValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,PCCompanyKPIMonthlyValueID 'AttributeId'
					,NULL 'OldValue'
					,KPIBudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCCompanyKPIMonthlyValue A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND PCCompanyKPIMonthlyValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,CompanyKPIForecastValueId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CompanyKPIForecastValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND CompanyKPIForecastValueId = @AttributeId
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 6
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'ImpactKPIs'
				) 'ModuleName'
			,ImpactKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_ImpactKPI
		WHERE ImpactKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM ImpactKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,A.AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM ImpactKpiAuditLog A
			--LEFT JOIN  PCImpactKPIQuarterlyValue P ON P.
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,ImpactKPIID 'AttributeId'
				,NULL 'OldValue'
				,KPIActualValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCImpactKPIQuarterlyValue A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PCImpactKPIQuarterlyValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 7
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'ProfitLoss'
				) 'ModuleName'
			,ProfitAndLossLineItemID 'KpiId'
			,ProfitAndLossLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_ProfitAndLoss_LineItems
		WHERE ProfitAndLossLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLoss_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				DECLARE @ValueTypeId INT;
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 8
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'BalanceSheet'
				) 'ModuleName'
			,BalanceSheetLineItemID 'KpiId'
			,BalanceSheetLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_BalanceSheet_LineItems
		WHERE BalanceSheetLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheet_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			  ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 9
	BEGIN
	SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CashFlow'
				) 'ModuleName'
			,CashFlowLineItemID 'KpiId'
			,CashFlowLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_CashFlow_LineItems
		WHERE CashFlowLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlow_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45)
	BEGIN
			SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = MstM.Name
				) 'ModuleName'
			,Mst.KpiId 'KpiId'
			,Mst.KPI 'Kpi'
			,Mst.KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM MCapTable Mst INNER JOIN M_KpiModules MstM on Mst.ModuleId=MstM.ModuleID
		WHERE Mst.KpiId = @KpiId
			AND Mst.ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM CapTableAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM CapTableAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND A.ModuleId = @ModuleId
				AND A.AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,A.PcCapTableValueId 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PcCapTableValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND A.ModuleId = @ModuleId
				AND A.PcCapTableValueId = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
	BEGIN
		SELECT (SELECT AliasName FROM M_SubPageFields WHERE NAME = MstM.Name) 'ModuleName',MasterKpiID 'KpiId',KPI 'Kpi',KpiInfo,(
				SELECT C.CurrencyCode FROM PortfolioCompanyDetails P INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE P.PortfolioCompanyID = @CompanyId) AS CurrencyCode
		FROM M_MasterKpis Mst INNER JOIN M_KpiModules MstM on Mst.ModuleId=MstM.ModuleID
		WHERE Mst.MasterKpiID = @KpiId AND Mst.ModuleID = @ModuleId

		IF EXISTS (SELECT TOP 1 * FROM MasterKpiAuditLog WHERE AttributeId = @AttributeId AND PortfolioCompanyId = @CompanyId AND ModuleId = @ModuleId)
		BEGIN
			SELECT A.AuditId,AttributeId,OldValue,NewValue,AuditType 'Source',D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId AND ModuleId = @ModuleId AND AttributeId = @AttributeId ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId',PCMasterKpiValueID 'AttributeId',NULL 'OldValue',KPIValue 'NewValue','Bulk Upload' Source,D.DocumentId,D.DocumentName,SupportingDocumentsId
			,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,SupportingDocumentsId,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId > 1000
	BEGIN
		SELECT (SELECT AliasName FROM M_SubPageFields WHERE NAME = MstM.Name) 'ModuleName',FundSectionKpiId 'KpiId',KPI 'Kpi',KpiInfo,(
				SELECT C.CurrencyCode FROM FundDetails P INNER JOIN M_Currency C ON P.CurrencyID = C.CurrencyID WHERE P.FundID = @CompanyId) AS CurrencyCode
		FROM MFundSectionKpi Mst INNER JOIN MFundKpiModules MstM on Mst.ModuleId=MstM.ModuleID
		WHERE Mst.FundSectionKpiId = @KpiId AND Mst.ModuleID = @ModuleId

		IF EXISTS (SELECT TOP 1 * FROM FundKpiAuditLog WHERE AttributeId = @AttributeId AND FundId = @CompanyId AND ModuleId = @ModuleId)
		BEGIN
			SELECT A.AuditId,AttributeId,OldValue,NewValue,AuditType 'Source',D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM FundKpiAuditLog A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE FundId = @CompanyId AND ModuleId = @ModuleId AND AttributeId = @AttributeId ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId',FundMasterKpiValueId 'AttributeId',NULL 'OldValue',KPIValue 'NewValue','Bulk Upload' Source,D.DocumentId,D.DocumentName,SupportingDocumentsId
			,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,SupportingDocumentsId,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM FundMasterKpiValues A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id ORDER BY AuditId DESC
		END
	END
	SET NOCOUNT OFF;
END
GO
ALTER PROCEDURE [dbo].[SpGetCompanyWiseMappedKPIs](
    @ModuleId INT = 0,
    @CompanyId INT = 0)
AS
BEGIN
 DECLARE @InvestmentKPI INT = 4,
            @MasterKPI INT = 1,
            @MasterKPI2 INT = 2,
            @ImpactKPI INT = 6,
            @OperationalKPI INT = 3,
            @CompanyKPI INT = 5,
			@ProfitAndLoss INT = 7,
			@BalanceSheet INT = 8,
			@CashFlow INT = 9;
IF(@ModuleId=@InvestmentKPI)
BEGIN
SELECT  MSt.KPI LineItem,Map.KpiID Id,Map.DisplayOrder,CASE WHEN Map.ParentKPIID IS NULL OR Map.ParentKPIID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId
    FROM Mapping_PortfolioInvestmentKPI Map
	  INNER JOIN M_InvestmentKPI MSt On MSt.InvestmentKPIId=Map.KpiID 
            WHERE Map.IsDeleted=0  and MSt.IsDeleted=0  AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder
END
IF(@ModuleId=@MasterKPI or @ModuleId=@MasterKPI2 OR @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30,46,47,48,49,50,51,52,53,54,55))
BEGIN
SELECT  MSt.KPI LineItem,Map.KpiID Id,Map.DisplayOrder,CASE WHEN Map.ParentKPIID IS NULL OR Map.ParentKPIID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId
    FROM Mapping_Kpis Map
	  INNER JOIN M_MasterKpis MSt On MSt.MasterKpiID=Map.KpiID 
            WHERE Map.IsDeleted=0  and MSt.IsDeleted=0 and MSt.ModuleID=@ModuleId AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder
END
IF(@ModuleId=@ImpactKPI)
BEGIN
SELECT MSt.KPI LineItem,Map.ImpactKPIID Id,Map.KPIOrder,CASE WHEN Map.ParentKPIID IS NULL OR Map.ParentKPIID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId
    FROM Mapping_ImpactKPI_Order Map
	  INNER JOIN M_ImpactKPI MSt On MSt.ImpactKPIID=Map.ImpactKPIID 
            WHERE Map.IsDeleted=0  and MSt.IsDeleted=0 AND Map.PortfolioCompanyID=@CompanyId  ORDER BY Map.KPIOrder
END
IF(@ModuleId=@OperationalKPI)
BEGIN
SELECT MSt.KPI LineItem,Map.KpiID Id,Map.DisplayOrder,CASE WHEN Map.ParentKPIID IS NULL OR Map.ParentKPIID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId
    FROM Mapping_PortfolioOperationalKPI Map
	  INNER JOIN M_SectorwiseOperationalKPI MSt On MSt.SectorwiseOperationalKPIID=Map.KpiID 
            WHERE Map.IsDeleted=0  and MSt.IsDeleted=0  AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder
END
IF(@ModuleId=@CompanyKPI)
BEGIN
SELECT  MSt.KPI LineItem,Map.KpiID Id,Map.DisplayOrder,CASE WHEN Map.ParentKPIID IS NULL OR Map.ParentKPIID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId FROM  Mapping_PortfolioCompanyKPI Map 
	INNER JOIN M_CompanyKPI MSt
	on mst.CompanyKPIID=Map.KpiID WHERE Map.IsDeleted=0 and mst.IsDeleted=0 AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder
END
IF(@ModuleId=@ProfitAndLoss)
BEGIN
SELECT  MSt.ProfitAndLossLineItem LineItem,Map.ProfitAndLossLineItemID Id,Map.DisplayOrder,CASE WHEN Map.ParentLineItemID IS NULL OR Map.ParentLineItemID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId FROM Mapping_CompanyProfitAndLossLineItems Map 
	INNER JOIN M_ProfitAndLoss_LineItems MSt on mst.ProfitAndLossLineItemID=Map.ProfitAndLossLineItemID and mst.IsDeleted=0
	WHERE Map.IsDeleted=0 and mst.IsDeleted=0 AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder

END
IF(@ModuleId=@BalanceSheet)
BEGIN
SELECT  MSt.BalanceSheetLineItem LineItem,Map.BalanceSheetLineItemID Id,Map.DisplayOrder,CASE WHEN Map.ParentLineItemID IS NULL OR Map.ParentLineItemID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId FROM Mapping_CompanyBalanceSheetLineItems Map 
	INNER JOIN M_BalanceSheet_LineItems MSt on mst.BalanceSheetLineItemID=Map.BalanceSheetLineItemID and mst.IsDeleted=0
	WHERE Map.IsDeleted=0 and mst.IsDeleted=0 AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder

END
IF(@ModuleId=@CashFlow)
BEGIN
SELECT  MSt.CashFlowLineItem LineItem,Map.CashFlowLineItemID Id,Map.DisplayOrder,CASE WHEN Map.ParentLineItemID IS NULL OR Map.ParentLineItemID=0 THEN 1 ELSE MSt.IsHeader END AS IsHeader,Map.PortfolioCompanyID PortfolioCompanyId FROM Mapping_CompanyCashFlowLineItems Map 
	INNER JOIN M_CashFlow_LineItems MSt on mst.CashFlowLineItemID=Map.CashFlowLineItemID and mst.IsDeleted=0
	WHERE Map.IsDeleted=0 and mst.IsDeleted=0 AND Map.PortfolioCompanyID=@CompanyId ORDER BY Map.DisplayOrder

END
END
GO
-- Index for ManagedAccountDetails table to improve performance on name lookups
IF NOT EXISTS (
    SELECT *
    FROM sys.indexes
    WHERE name='IX_ManagedAccountDetails_ManagedAccountName'
    AND object_id = OBJECT_ID('dbo.ManagedAccountDetails')
)
BEGIN
    CREATE NONCLUSTERED INDEX IX_ManagedAccountDetails_ManagedAccountName
    ON dbo.ManagedAccountDetails ([ManagedAccountName], [IsActive], [IsDeleted]);
END
GO
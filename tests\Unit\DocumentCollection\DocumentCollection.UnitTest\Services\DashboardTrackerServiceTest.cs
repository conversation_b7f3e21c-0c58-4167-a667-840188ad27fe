﻿using Contract.PortfolioCompany;
using Contract.Repository;
using Contract.Utility;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.DashboardTracker;
using DataAccessLayer.Models.Tracker;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Models;
using DocumentCollection.DashboardTracker.Services;
using Moq;
using System.Linq.Expressions;
using Workflow;
using Workflow.Models;

namespace DocumentCollection.UnitTest.Services
{
    public class DashboardTrackerServiceTest
    {
        private readonly Mock<IWorkflowPCService> _workflowPCServiceMock;
        private readonly Mock<IFileService> _fileServiceMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly DashboardTrackerService _service;
        private object totalRecords;

        public DashboardTrackerServiceTest()
        {
            _workflowPCServiceMock = new Mock<IWorkflowPCService>();
            _fileServiceMock = new Mock<IFileService>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _service = new DashboardTrackerService(_workflowPCServiceMock.Object, _fileServiceMock.Object, _unitOfWorkMock.Object);
        }

        private Mock<IGenericRepository<DashboardTrackerConfig>> SetupConfigRepo(List<DashboardTrackerConfig> configs)
        {
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(configs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            return repoMock;
        }

        [Fact]
        public async Task GetPortfolioCompanies_ReturnsEmpty_WhenNoCompanies()
        {
            // Arrange
            var filter = new PortfolioCompanyFilter();
            _workflowPCServiceMock.Setup(x => x.GetPortfolioCompanies(filter)).ReturnsAsync((WorkflowPCModel)null);

            // Act
            var result = await _service.GetPortfolioCompanies(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfigAsync_UpdatesExistingConfig()
        {
            // Arrange
            var dto = new DashboardTrackerConfigDto
            {
                ID = 10,
                FieldType = 1,
                DataType = 2,
                Name = "Test",
                FrequencyType = 1,
                StartPeriod = "2023",
                EndPeriod = "2024",
                IsPrefix = true,
                TimeSeriesDateFormat = "yyyy-MM",
                MapTo = 1,
                IsActive = true,
                IsDeleted = false,
                ModifiedBy = 2,
                ModifiedOn = System.DateTime.UtcNow
            };

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            repoMock.Setup(x => x.Update(It.IsAny<DashboardTrackerConfig>())).Verifiable();
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardTrackerConfigAsync(dto);

            // Assert
            repoMock.Verify(x => x.Update(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
            Assert.True(result > 0);
        }

        [Fact]
        public async Task GetDashboardColumnsAsync_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerConfig>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            // Act
            var result = await _service.GetDashboardColumnsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task SaveTrackerDropdownValuesAsync_ReturnsFalse_WhenDtoIsNull()
        {
            // Act
            var result = await _service.SaveTrackerDropdownValuesAsync(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SaveTrackerDropdownValuesAsync_ReturnsFalse_WhenDropdownValuesEmpty()
        {
            // Arrange
            var dropdownlist = new List<TrackerDropdownValueWithType> {
                new TrackerDropdownValueWithType
                {
                    DropdownValues = new List<string>(),
                    DropdownType = 1
                }
            };

            var dto = new TrackerDropdownValueDto { DropdownValueWithType = dropdownlist };
            // Act
            var result = await _service.SaveTrackerDropdownValuesAsync(dto);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfigAsync_InsertsNewConfig()
        {
            // Arrange
            var dto = new DashboardTrackerConfigDto
            {
                FieldType = 1,
                DataType = 2,
                Name = "Test",
                FrequencyType = 1,
                StartPeriod = "2023",
                EndPeriod = "2024",
                IsPrefix = true,
                TimeSeriesDateFormat = "yyyy-MM",
                MapTo = 1,
                IsActive = true,
                IsDeleted = false,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow
            };

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            repoMock.Setup(x => x.Insert(It.IsAny<DashboardTrackerConfig>())).Verifiable();
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardTrackerConfigAsync(dto);

            // Assert
            repoMock.Verify(x => x.Insert(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
            Assert.True(result >= 0);
        }

        [Fact]
        public async Task GetDashboardColumnsAsync_ReturnsColumns_WhenConfigsExist()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig { ID = 1, Name = "Col1", IsActive = true, IsDeleted = false }
            };
            SetupConfigRepo(configs);

            // Act
            var result = await _service.GetDashboardColumnsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("Col1", result[0].Name);
        }
        
        [Fact]
        public async Task GetAllTrackerConfigsAsync_ReturnsConfigsWithDropdowns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig { ID = 1, DataType = 4, Name = "Dropdown", IsDeleted = false }
            };
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(configs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            var dropdownRepoMock = new Mock<IGenericRepository<TrackerDropdownValue>>();
            dropdownRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<TrackerDropdownValue, bool>>>()))
                .ReturnsAsync(new List<TrackerDropdownValue>
                {
                    new TrackerDropdownValue { TackerFieldConfigId = 1, DropdownValue = "A" }
                });
            _unitOfWorkMock.SetupGet(x => x.TrackerDropdownValueRepository).Returns(dropdownRepoMock.Object);

            // Act
            var result = await _service.GetAllTrackerConfigsAsync();

            // Assert
            Assert.Single(result);
            Assert.Equal("Dropdown", result[0].Name);
            Assert.Contains("A", result[0].DropdownList);
        }

        [Fact]
        public async Task GetAllTrackerConfigsAsync_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerConfig>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            // Act
            var result = await _service.GetAllTrackerConfigsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task SaveDashboardCellValuesAsync_WithNewValues_ReturnsTrue()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Test Value"
                    }
                }
            };
            var userId = 123;

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync((DashboardTrackerCellValue)null); // No existing record

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardCellValuesAsync(dto, userId);

            // Assert
            Assert.True(result);
            cellValueRepoMock.Verify(x => x.Insert(It.IsAny<DashboardTrackerCellValue>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task SaveDashboardCellValuesAsync_WithExistingValues_UpdatesAndReturnsTrue()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Updated Value"
                    }
                }
            };
            var userId = 123;

            var existingEntity = new DashboardTrackerCellValue
            {
                ID = 1,
                PortfolioCompanyId = 1,
                FundId = 1,
                ColumnId = 1,
                TimeSeriesID = "1_1",
                CellValue = "Old Value",
                IsActive = true,
                IsDeleted = false
            };

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync(existingEntity);

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardCellValuesAsync(dto, userId);

            // Assert
            Assert.True(result);
            Assert.Equal("Updated Value", existingEntity.CellValue);
            Assert.Equal(userId, existingEntity.ModifiedBy);
            cellValueRepoMock.Verify(x => x.Update(existingEntity), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task SaveDashboardCellValuesAsync_WithException_ReturnsFalse()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Test Value"
                    }
                }
            };
            var userId = 123;

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ThrowsAsync(new Exception("Database error"));

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);

            // Act
            var result = await _service.SaveDashboardCellValuesAsync(dto, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SaveDashboardCellValuesAsync_WithMultipleValues_ProcessesAll()
        {
            // Arrange
            var dto = new SaveDashboardCellValuesDto
            {
                CellValues = new List<DashboardCellValueDto>
                {
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 1,
                        TimeSeriesID = "1_1",
                        CellValue = "Value 1"
                    },
                    new DashboardCellValueDto
                    {
                        PortfolioCompanyId = 1,
                        FundId = 1,
                        ColumnId = 2,
                        TimeSeriesID = "2_1",
                        CellValue = "Value 2"
                    }
                }
            };
            var userId = 123;

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync((DashboardTrackerCellValue)null); // No existing records

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardCellValuesAsync(dto, userId);

            // Assert
            Assert.True(result);
            cellValueRepoMock.Verify(x => x.Insert(It.IsAny<DashboardTrackerCellValue>()), Times.Exactly(2));
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        // ==================== DELETE DASHBOARD TRACKER COLUMNS TESTS ====================

        [Fact]
        public async Task DeleteDashboardTrackerColumnsAsync_WithTimeSeriesColumns_UpdatesDeletedColumns()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 1,
                    IsTimeSeries = true,
                    FieldType = (int)FieldTypeEnum.TimeSeries,
                    TimeSeriesID = "2023-Q1"
                },
                new ColumnsDto
                {
                    ID = 1,
                    IsTimeSeries = true,
                    FieldType = (int)FieldTypeEnum.TimeSeries,
                    TimeSeriesID = "2023-Q2"
                }
            };

            var existingConfig = new DashboardTrackerConfig
            {
                ID = 1,
                DeletedColumns = "2023-Q3"
            };

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(existingConfig);
            configRepoMock.Setup(x => x.Update(It.IsAny<DashboardTrackerConfig>())).Verifiable();

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.DeleteDashboardTrackerColumnsAsync(columns);

            // Assert
            Assert.True(result);
            configRepoMock.Verify(x => x.Update(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumnsAsync_WithNonTimeSeriesColumns_UpdatesDeletedColumns()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 2,
                    IsTimeSeries = false,
                    FieldType = (int)FieldTypeEnum.Data,
                    Name = "Revenue"
                }
            };

            var existingConfig = new DashboardTrackerConfig
            {
                ID = 2,
                DeletedColumns = "EBITDA"
            };

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(existingConfig);
            configRepoMock.Setup(x => x.Update(It.IsAny<DashboardTrackerConfig>())).Verifiable();

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.DeleteDashboardTrackerColumnsAsync(columns);

            // Assert
            Assert.True(result);
            configRepoMock.Verify(x => x.Update(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumnsAsync_WithDuplicateDeletedColumns_DoesNotAddDuplicates()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 3,
                    IsTimeSeries = false,
                    FieldType = (int)FieldTypeEnum.Data,
                    Name = "Revenue"
                }
            };

            var existingConfig = new DashboardTrackerConfig
            {
                ID = 3,
                DeletedColumns = "Revenue" // Already deleted
            };

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(existingConfig);

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.DeleteDashboardTrackerColumnsAsync(columns);

            // Assert
            Assert.True(result);
            configRepoMock.Verify(x => x.Update(It.IsAny<DashboardTrackerConfig>()), Times.Never);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumnsAsync_WithException_ReturnsFalse()
        {
            // Arrange
            var columns = new List<ColumnsDto>
            {
                new ColumnsDto
                {
                    ID = 4,
                    IsTimeSeries = true,
                    DataType = (int)FieldTypeEnum.TimeSeries,
                    TimeSeriesID = "2023-Q1"
                }
            };

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ThrowsAsync(new Exception("Database error"));

            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            // Act
            var result = await _service.DeleteDashboardTrackerColumnsAsync(columns);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteDashboardTrackerColumnsAsync_WithEmptyColumns_ReturnsTrue()
        {
            // Arrange
            var columns = new List<ColumnsDto>();
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(0);

            // Act
            var result = await _service.DeleteDashboardTrackerColumnsAsync(columns);

            // Assert
            Assert.False(result); // Returns false because no rows were affected
        }

        // ==================== GET DELETED COLUMNS TESTS ====================

        [Fact]
        public async Task GetDeletedColumnsAsync_WithDeletedColumns_ReturnsData()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var dashboardConfigs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = (int)FieldTypeEnum.TimeSeries,
                    DataType = (int)DataTypeEnum.Number,
                    Name = "Revenue",
                    StartPeriod = "2023-Q1",
                    EndPeriod = "2023-Q4",
                    FrequencyType = 2,
                    TimeSeriesDateFormat = "yyyy-Qq",
                    IsPrefix = true,
                    DeletedColumns = "2023-Q1,2023-Q2"
                },
                new DashboardTrackerConfig
                {
                    ID = 2,
                    FieldType = (int)FieldTypeEnum.Data,
                    DataType = (int)DataTypeEnum.Text,
                    Name = "Company Description",
                    DeletedColumns = "Company Description"
                }
            };

            var portfolioCompanies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 1, FundId = 1, FundName = "Fund A", CompanyName = "Company A" },
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 2, FundId = 1, FundName = "Fund A", CompanyName = "Company B" }
            };

            var workflowResult = new WorkflowPCModel
            {
                PortfolioCompanyQueryListModel = new PortfolioCompanyQueryListModel
                {
                    PortfolioCompanyList = portfolioCompanies
                }
            };

            // Setup mocks
            _workflowPCServiceMock.Setup(x => x.GetPortfolioCompanies(It.Is<PortfolioCompanyFilter>(f => f.CreatedBy == userId)))
                .ReturnsAsync(workflowResult);

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(dashboardConfigs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerCellValue>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);

            var customFieldRepoMock = new Mock<IGenericRepository<PageConfigurationFieldValue>>();
            customFieldRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationFieldValue, bool>>>()))
                .ReturnsAsync(new List<PageConfigurationFieldValue>());
            _unitOfWorkMock.SetupGet(x => x.PageConfigurationFieldValueRepository).Returns(customFieldRepoMock.Object);

            var dropdownRepoMock = new Mock<IGenericRepository<TrackerDropdownValue>>();
            dropdownRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<TrackerDropdownValue, bool>>>()))
                .ReturnsAsync(new List<TrackerDropdownValue>());
            _unitOfWorkMock.SetupGet(x => x.TrackerDropdownValueRepository).Returns(dropdownRepoMock.Object);

            // Mock PortfolioCompanyDetailRepository for logo fetching
            var portfolioCompanyDetailRepoMock = new Mock<IGenericRepository<PortfolioCompanyDetails>>();
            portfolioCompanyDetailRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync((PortfolioCompanyDetails)null);
            _unitOfWorkMock.SetupGet(x => x.PortfolioCompanyDetailRepository).Returns(portfolioCompanyDetailRepoMock.Object);

            // Act
            var result = await _service.GetDeletedColumnsAsync(userId, filter);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Columns);
            Assert.True(result.Columns.Count > 0);
            Assert.NotNull(result.Data);
            Assert.True(result.TotalRecords > 0);
        }

        [Fact]
        public async Task GetDeletedColumnsAsync_WithNoDeletedColumns_ReturnsEmptyResult()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerConfig>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            // Act
            var result = await _service.GetDeletedColumnsAsync(userId, filter);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Columns);
            Assert.Empty(result.Columns);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalRecords);
        }

        [Fact]
        public async Task GetDeletedColumnsAsync_WithException_ReturnsFailureResult()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ThrowsAsync(new Exception("Database error"));
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            // Act
            var result = await _service.GetDeletedColumnsAsync(userId, filter);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.NotNull(result.Columns);
            Assert.Empty(result.Columns);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalRecords);
        }

        [Fact]
        public async Task GetDeletedColumnsAsync_WithPagination_AppliesPaginationCorrectly()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 5, First = 5 }; // Page 2, 5 rows per page
            var userId = 123;

            var dashboardConfigs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = (int)FieldTypeEnum.Data,
                    DataType = (int)DataTypeEnum.Text,
                    Name = "Test Column",
                    DeletedColumns = "Test Column"
                }
            };

            var portfolioCompanies = Enumerable.Range(1, 20)
                .Select(i => new PortfolioCompanyQueryModel 
                { 
                    PortfolioCompanyID = i, 
                    FundId = 1, 
                    FundName = "Fund A", 
                    CompanyName = $"Company {i}" 
                })
                .ToList();

            var workflowResult = new WorkflowPCModel
            {
                PortfolioCompanyQueryListModel = new PortfolioCompanyQueryListModel
                {
                    PortfolioCompanyList = portfolioCompanies
                }
            };

            // Setup mocks
            _workflowPCServiceMock.Setup(x => x.GetPortfolioCompanies(It.IsAny<PortfolioCompanyFilter>()))
                .ReturnsAsync(workflowResult);

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(dashboardConfigs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerCellValue>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);

            var customFieldRepoMock = new Mock<IGenericRepository<PageConfigurationFieldValue>>();
            customFieldRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationFieldValue, bool>>>()))
                .ReturnsAsync(new List<PageConfigurationFieldValue>());
            _unitOfWorkMock.SetupGet(x => x.PageConfigurationFieldValueRepository).Returns(customFieldRepoMock.Object);

            var dropdownRepoMock = new Mock<IGenericRepository<TrackerDropdownValue>>();
            dropdownRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<TrackerDropdownValue, bool>>>()))
                .ReturnsAsync(new List<TrackerDropdownValue>());
            _unitOfWorkMock.SetupGet(x => x.TrackerDropdownValueRepository).Returns(dropdownRepoMock.Object);

            // Mock PortfolioCompanyDetailRepository for logo fetching
            var portfolioCompanyDetailRepoMock = new Mock<IGenericRepository<PortfolioCompanyDetails>>();
            portfolioCompanyDetailRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync((PortfolioCompanyDetails)null);
            _unitOfWorkMock.SetupGet(x => x.PortfolioCompanyDetailRepository).Returns(portfolioCompanyDetailRepoMock.Object);

            // Act
            var result = await _service.GetDeletedColumnsAsync(userId, filter);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(20, result.TotalRecords); // Total companies
            Assert.True(result.Data.Count <= 5); // Should respect page size
        }

        [Fact]
        public async Task GetDeletedColumnsAsync_WithTimeSeriesColumns_GeneratesCorrectColumnNames()
        {
            // Arrange
            var filter = new PaginationFilter { Rows = 10, First = 0 };
            var userId = 123;

            var dashboardConfigs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = (int)FieldTypeEnum.TimeSeries,
                    DataType = (int)DataTypeEnum.Number,
                    Name = "Revenue",
                    StartPeriod = "2023-Q1",
                    EndPeriod = "2023-Q4",
                    FrequencyType = 2, // Quarterly
                    TimeSeriesDateFormat = "yyyy-Qq",
                    IsPrefix = true,
                    DeletedColumns = "2023-Q1,2023-Q2"
                }
            };

            var portfolioCompanies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 1, FundId = 1, FundName = "Fund A", CompanyName = "Company A" }
            };

            var workflowResult = new WorkflowPCModel
            {
                PortfolioCompanyQueryListModel = new PortfolioCompanyQueryListModel
                {
                    PortfolioCompanyList = portfolioCompanies
                }
            };

            // Setup mocks
            _workflowPCServiceMock.Setup(x => x.GetPortfolioCompanies(It.IsAny<PortfolioCompanyFilter>()))
                .ReturnsAsync(workflowResult);

            var configRepoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            configRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(dashboardConfigs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(configRepoMock.Object);

            var cellValueRepoMock = new Mock<IGenericRepository<DashboardTrackerCellValue>>();
            cellValueRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerCellValue, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerCellValue>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerCellValueRepository).Returns(cellValueRepoMock.Object);

            var customFieldRepoMock = new Mock<IGenericRepository<PageConfigurationFieldValue>>();
            customFieldRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationFieldValue, bool>>>()))
                .ReturnsAsync(new List<PageConfigurationFieldValue>());
            _unitOfWorkMock.SetupGet(x => x.PageConfigurationFieldValueRepository).Returns(customFieldRepoMock.Object);

            var dropdownRepoMock = new Mock<IGenericRepository<TrackerDropdownValue>>();
            dropdownRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<TrackerDropdownValue, bool>>>()))
                .ReturnsAsync(new List<TrackerDropdownValue>());
            _unitOfWorkMock.SetupGet(x => x.TrackerDropdownValueRepository).Returns(dropdownRepoMock.Object);

            // Mock PortfolioCompanyDetailRepository for logo fetching
            var portfolioCompanyDetailRepoMock = new Mock<IGenericRepository<PortfolioCompanyDetails>>();
            portfolioCompanyDetailRepoMock.Setup(x => x.FindFirstAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync((PortfolioCompanyDetails)null);
            _unitOfWorkMock.SetupGet(x => x.PortfolioCompanyDetailRepository).Returns(portfolioCompanyDetailRepoMock.Object);

            // Act
            var result = await _service.GetDeletedColumnsAsync(userId, filter);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Columns);
            
            var timeSeriesColumns = result.Columns.Where(c => c.IsTimeSeries).ToList();
            Assert.True(timeSeriesColumns.Count > 0);
            
            // Check that time series columns have proper names and TimeSeriesID
            foreach (var column in timeSeriesColumns)
            {
                Assert.True(column.IsTimeSeries);
                Assert.NotNull(column.TimeSeriesID);
                Assert.Contains("Revenue", column.Name); // Should contain the base name
            }
        }
    }
}
